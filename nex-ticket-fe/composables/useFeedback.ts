import { useToast } from 'vue-toastification'

export function useFeedback() {
  const rollbar = useRollbar()
  const toast = useToast()
  interface rollbar_options { rollbar: boolean, level: 'warning' | 'error' | 'critical', extras?: any }
  const rollbar_level = {
    error: 'error',
    warning: 'warning',
    critical: 'critical',
  }

  const rollbarError = (message: string, options: rollbar_options) => {
    if (!options.rollbar)
      return
    switch (options.level) {
      case rollbar_level.error:
        rollbar.error(message, options)
        console.error(message)
        break

      case rollbar_level.warning:
        rollbar.warning(message)
        console.warn(message)
        break

      case rollbar_level.critical:
        rollbar.critical(message)
        console.error(message)
    }
  }

  const error = (message: string, options: rollbar_options) => {
    toast.error(message)
    rollbarError(message, options)
  }

  const success = (message: string) => {
    toast.success(message)
  }

  const info = (message: string) => {
    toast.info(message)
    rollbar.info(message)
  }

  return {
    error,
    rollbar_level,
    success,
    info,
  }
}
