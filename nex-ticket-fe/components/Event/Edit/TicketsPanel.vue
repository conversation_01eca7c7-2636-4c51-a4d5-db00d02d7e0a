<script lang="ts" setup>
import { Delete02Icon } from 'hugeicons-vue'
import { array as yupArray, date as yupDate, number as yupNumber, object as yupObject, ref as yupRef, string as yupString } from 'yup'

const { t } = useI18n()
const yup_t = (key: string) => t(`organiser.edit_event.tickets_comp.yup_texts.${key}`)

const schema_step_5 = yupObject({
  ticket_types_attributes: yupArray().of(
    yupObject({
      name: yupString().required(yup_t('name_req')),
      max_amount: yupNumber()
        .required(yup_t('max_amount_req'))
        .min(1, yup_t('max_amount_min')),
      price: yupNumber()
        .required(yup_t('price_req'))
        .min(0, yup_t('price_min')),
      discounts: yupArray().of(
        yupObject({
          percentage: yupNumber()
            .required(yup_t('percentage_req'))
            .min(1, yup_t('percentage_min'))
            .max(100, yup_t('percentage_max')),
          start_date: yupDate()
            .nullable()
            .transform((value, originalValue) => (originalValue === '' ? null : value))
            .required(yup_t('start_date_req')),
          end_date: yupDate()
            .nullable()
            .transform((value, originalValue) => (originalValue === '' ? null : value))
            .required(yup_t('end_date_req'))
            .min(yupRef('start_date'), yup_t('end_date_min')),
        }),
      ).notRequired(),
      description: yupString()
        .required(yup_t('description_req'))
        .min(10, yup_t('description_min'))
        .max(2500, yup_t('description_max')),
    }),
  ).notRequired(),
})

const { validate: validateStep5, errors: errorsStep5 } = useForm({
  validationSchema: schema_step_5,
})

const eventData = defineModel<{
  id: number | undefined
  name: string
  max_amount: number
  price: number
  discounts: { percentage: number, start_date: Date, end_date: Date }[]
  description: string
  disabled: boolean
}[]>('eventData')
const validateFunc = defineModel<() => Promise<boolean>>('validateFunc')

const { fields: ticketTypes, push: addTicketType, remove: deleteTicketType, update: updateTicketType } = useFieldArray<{
  id: number | undefined
  name: string
  max_amount: number
  price: number
  discounts: { percentage: number, start_date: string, end_date: string }[]
  description: string
  disabled: boolean
}>('ticket_types_attributes')

onMounted(async () => {
  validateFunc.value = validateFields
  setExistingEventData()
})

function setExistingEventData() {
  if (eventData.value) {
    eventData.value.forEach((type) => {
      addTicketType({
        id: type.id,
        name: type.name,
        max_amount: type.max_amount,
        price: Number(type.price),
        discounts: type.discounts.map(discount => ({
          ...discount,
          start_date: toLocalISOString(discount.start_date).slice(0, 16),
          end_date: toLocalISOString(discount.end_date).slice(0, 16),
        })),
        description: type.description,
        disabled: type.disabled,
      })
    })
  }
  else {
    eventData.value = []
  }
}

async function validateFields(): Promise<boolean> {
  const { valid } = await validateStep5()
  return valid
}

function onTicketTypeCreate() {
  if (eventData.value) {
    eventData.value.push({
      id: undefined,
      name: '',
      max_amount: 0,
      price: 0,
      discounts: [],
      description: '',
      disabled: false,
    })
  }
}
function onTicketTypeDelete(index: number) {
  if (eventData.value) {
    eventData.value = eventData.value?.filter((_, i) => i !== index)
  }
}
function onTicketTypeNameChange(index: number, newName: string) {
  if (eventData.value) {
    eventData.value[index].name = newName
  }
}

function onTicketTypeMaxChange(index: number, newMaxAamount: number) {
  if (eventData.value) {
    eventData.value[index].max_amount = newMaxAamount
  }
}
function onTicketTypePriceChange(index: number, newPrice: number) {
  if (eventData.value) {
    eventData.value[index].price = newPrice
  }
}

// Changing values for ticket discounts
function onTicketDiscCreate(index: number) {
  if (eventData.value) {
    eventData.value[index].discounts.push({
      percentage: 10,
      start_date: new Date(),
      end_date: new Date(),
    })

    return eventData.value[index].discounts.map(discount => ({
      ...discount,
      start_date: toLocalISOString(discount.start_date).slice(0, 16),
      end_date: toLocalISOString(discount.end_date).slice(0, 16),
    }))
  }
}
function onTicketDiscDelete(index: number, discountIndex: number) {
  if (eventData.value) {
    eventData.value[index].discounts = eventData.value[index].discounts.filter((_, i) => i !== discountIndex)
  }
}

function onTicketDiscPercChange(index: number, discountIndex: number, newPercentage: number) {
  if (eventData.value) {
    eventData.value[index].discounts[discountIndex].percentage = newPercentage
  }
}
function onTicketDiscStartChange(index: number, discountIndex: number, newStartDate: string) {
  if (eventData.value) {
    eventData.value[index].discounts[discountIndex].start_date = new Date(newStartDate)
  }
}
function onTicketDiscEndChange(index: number, discountIndex: number, newEndDate: string) {
  if (eventData.value) {
    eventData.value[index].discounts[discountIndex].end_date = new Date(newEndDate)
  }
}

function onTicketTypeDescChange(index: number, newDesc: string) {
  if (eventData.value) {
    eventData.value[index].description = newDesc
  }
}

function toggleDisable(id: number, disabled: boolean) {
  if (eventData.value.filter(event => !event.disabled).length === 1 && !disabled) {
    console.error(t('errors.event_errors.disable_last_ticket_type_error'))
    return
  }
  const index = eventData.value.findIndex(ticket => ticket.id === id)
  if (index !== -1) {
    eventData.value[index].disabled = !eventData.value[index].disabled
  }
  const ticket = ticketTypes.value.find(ticketType => ticketType.value.id === id)
  ticket.value.disabled = !disabled
}
</script>

<template>
  <v-card class="!shadow-none">
    <div v-for="(ticketType, index) in ticketTypes" :key="ticketType.key">
      <v-card
        class="md:mx-4
            mx-3 mt-3 mb-8"
      >
        <!-- PC version for ticket type values -->
        <div class="hidden md:block">
          <v-row>
            <v-col cols="4">
              <v-text-field
                v-model="ticketType.value.name"
                class="md:text-base-normal"
                :label="$t('organiser.edit_event.tickets_comp.labels.ticket_name')"
                :placeholder="$t('organiser.edit_event.tickets_comp.place_holders.ticket_type_name')"
                required
                :error-messages="errorsStep5[`ticket_types_attributes[${index}].name`]"
                @update:model-value="val => {
                  updateTicketType(index, { ...ticketType.value, name: val })
                  onTicketTypeNameChange(index, val)
                }"
              />
            </v-col>
            <v-col cols="2">
              <v-text-field
                v-model="ticketType.value.max_amount"
                class="md:text-base-normal"
                :label="$t('organiser.edit_event.tickets_comp.labels.max_amount')"
                type="number"
                required
                :error-messages="errorsStep5[`ticket_types_attributes[${index}].max_amount`]"
                @update:model-value="val => {
                  updateTicketType(index, { ...ticketType.value, max_amount: Number(val) })
                  onTicketTypeMaxChange(index, Number(val))
                }"
              />
            </v-col>
            <v-col cols="2">
              <v-text-field
                v-model="ticketType.value.price"
                class="md:text-base-normal"
                :label="$t('organiser.edit_event.tickets_comp.labels.price')"
                type="number"
                required
                :error-messages="errorsStep5[`ticket_types_attributes[${index}].price`]"
                @update:model-value="val => {
                  updateTicketType(index, { ...ticketType.value, price: Number(val) })
                  onTicketTypePriceChange(index, Number(val))
                }"
              />
            </v-col>
            <v-col cols="4">
              <v-btn
                class="md:float-end md:mt-3 md:me-3"
                icon
                color="#dc2626"
                @click="onTicketTypeDelete(index), deleteTicketType(index)"
              >
                <Delete02Icon />
              </v-btn>
              <v-btn
                class="md:float-start md:mt-4 md:me-3"
                color="#1e293b"
                @click="toggleDisable(ticketType.value.id, ticketType.value.disabled)"
              >
                {{ ticketType.value.disabled ? t('common.enable') : t('common.disable') }}
              </v-btn>
            </v-col>
          </v-row>
        </div>

        <!-- Phone version for ticket type values -->
        <div class="md:hidden">
          <v-row>
            <v-col cols="8">
              <v-text-field
                v-model="ticketType.value.name"
                class="text-base-normal"
                :label="$t('organiser.edit_event.tickets_comp.labels.ticket_name')"
                :placeholder="$t('organiser.edit_event.tickets_comp.place_holders.ticket_type_name')"
                required
                :error-messages="errorsStep5[`ticket_types_attributes[${index}].name`]"
                @update:model-value="val => {
                  updateTicketType(index, { ...ticketType.value, name: val })
                  onTicketTypeNameChange(index, val)
                }"
              />
            </v-col>
            <v-col cols="4">
              <v-btn
                class="float-end mt-3 me-3"
                icon
                color="#dc2626"
                @click="onTicketTypeDelete(index), deleteTicketType(index)"
              >
                <Delete02Icon />
              </v-btn>
            </v-col>
          </v-row>
          <v-row dense class="px-2">
            <v-col cols="6">
              <v-text-field
                v-model="ticketType.value.max_amount"
                class="text-base-normal"
                :label="$t('organiser.edit_event.tickets_comp.labels.max_amount')"
                type="number"
                required
                :error-messages="errorsStep5[`ticket_types_attributes[${index}].max_amount`]"
                @update:model-value="val => {
                  updateTicketType(index, { ...ticketType.value, max_amount: Number(val) })
                  onTicketTypeMaxChange(index, Number(val))
                }"
              />
            </v-col>
            <v-col cols="6">
              <v-text-field
                v-model="ticketType.value.price"
                class="text-base-normal"
                :label="$t('organiser.edit_event.tickets_comp.labels.price')"
                type="number"
                required
                :error-messages="errorsStep5[`ticket_types_attributes[${index}].price`]"
                @update:model-value="val => {
                  updateTicketType(index, { ...ticketType.value, price: Number(val) })
                  onTicketTypePriceChange(index, Number(val))
                }"
              />
            </v-col>
          </v-row>
        </div>

        <!-- Discounts Section -->
        <v-card-text>
          <v-card max-width="100%">
            <v-card-title class="text-base-normal">
              {{ $t('organiser.edit_event.tickets_comp.discounts') }}
            </v-card-title>
            <div
              v-for="(discount, discountIndex) in ticketType.value.discounts"
              :key="discountIndex"
            >
              <!-- PC version for discnounds -->
              <div class="hidden md:block">
                <v-row>
                  <v-col cols="2">
                    <v-text-field
                      class="md:text-base-normal md:ms-4"
                      :label="$t('organiser.edit_event.tickets_comp.labels.percentage')"
                      type="number"
                      :model-value="discount.percentage"
                      :error-messages="errorsStep5[`ticket_types_attributes[${index}].discounts[${discountIndex}].percentage`]"
                      @update:model-value="val => {
                        updateTicketType(index, { ...ticketType.value, discounts: ticketType.value.discounts.map((d, i) => i === discountIndex ? { ...d, percentage: Number(val) } : d) })
                        onTicketDiscPercChange(index, discountIndex, Number(val))
                      }"
                    />
                  </v-col>
                  <v-col cols="3">
                    <v-text-field
                      class="md:text-base-normal"
                      :label="$t('organiser.edit_event.tickets_comp.labels.start_date')"
                      type="datetime-local"
                      :model-value="discount.start_date"
                      :error-messages="errorsStep5[`ticket_types_attributes[${index}].discounts[${discountIndex}].start_date`]"
                      @update:model-value="val => {
                        updateTicketType(index, { ...ticketType.value, discounts: ticketType.value.discounts.map((d, i) => i === discountIndex ? { ...d, start_date: val } : d) })
                        onTicketDiscStartChange(index, discountIndex, val)
                      }"
                    />
                  </v-col>
                  <v-col cols="3">
                    <v-text-field
                      class="md:text-base-normal"
                      :label="$t('organiser.edit_event.tickets_comp.labels.end_date')"
                      type="datetime-local"
                      :model-value="discount.end_date"
                      :error-messages="errorsStep5[`ticket_types_attributes[${index}].discounts[${discountIndex}].end_date`]"
                      @update:model-value="val => {
                        updateTicketType(index, { ...ticketType.value, discounts: ticketType.value.discounts.map((d, i) => i === discountIndex ? { ...d, end_date: val } : d) })
                        onTicketDiscEndChange(index, discountIndex, val)
                      }"
                    />
                  </v-col>
                  <v-col cols="3">
                    <v-btn
                      class="md:mt-1"
                      icon
                      color="#dc2626"
                      @click="
                        updateTicketType(index, { ...ticketType.value, discounts: ticketType.value.discounts.filter((_, i) => i !== discountIndex) }),
                        onTicketDiscDelete(index, discountIndex)
                      "
                    >
                      <Delete02Icon />
                    </v-btn>
                  </v-col>
                </v-row>
              </div>

              <!-- Mobile version for discnounds -->
              <div class="md:hidden">
                <div class="flex">
                  <v-col cols="9">
                    <v-text-field
                      class="text-base-normal"
                      :label="$t('organiser.edit_event.tickets_comp.labels.percentage')"
                      type="number"
                      :model-value="discount.percentage"
                      :error-messages="errorsStep5[`ticket_types_attributes[${index}].discounts[${discountIndex}].percentage`]"
                      @update:model-value="val => {
                        updateTicketType(index, { ...ticketType.value, discounts: ticketType.value.discounts.map((d, i) => i === discountIndex ? { ...d, percentage: Number(val) } : d) })
                        onTicketDiscPercChange(index, discountIndex, Number(val))
                      }"
                    />
                  </v-col>
                  <v-col cols="3">
                    <v-btn
                      class="mt-1 ms-1"
                      icon
                      color="#dc2626"
                      @click="
                        updateTicketType(index, { ...ticketType.value, discounts: ticketType.value.discounts.filter((_, i) => i !== discountIndex) }),
                        onTicketDiscDelete(index, discountIndex)
                      "
                    >
                      <Delete02Icon />
                    </v-btn>
                  </v-col>
                </div>
                <v-col cols="12">
                  <v-text-field
                    class="text-base-normal -mt-5"
                    :label="$t('organiser.edit_event.tickets_comp.labels.start_date')"
                    type="datetime-local"
                    :model-value="discount.start_date"
                    :error-messages="errorsStep5[`ticket_types_attributes[${index}].discounts[${discountIndex}].start_date`]"
                    @update:model-value="val => {
                      updateTicketType(index, { ...ticketType.value, discounts: ticketType.value.discounts.map((d, i) => i === discountIndex ? { ...d, start_date: val } : d) })
                      onTicketDiscStartChange(index, discountIndex, val)
                    }"
                  />
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    class="text-base-normal -mt-5"
                    :label="$t('organiser.edit_event.tickets_comp.labels.end_date')"
                    type="datetime-local"
                    :model-value="discount.end_date"
                    :error-messages="errorsStep5[`ticket_types_attributes[${index}].discounts[${discountIndex}].end_date`]"
                    @update:model-value="val => {
                      updateTicketType(index, { ...ticketType.value, discounts: ticketType.value.discounts.map((d, i) => i === discountIndex ? { ...d, end_date: val } : d) })
                      onTicketDiscEndChange(index, discountIndex, val)
                    }"
                  />
                </v-col>
              </div>
            </div>
            <v-btn
              color="#1e293b"
              class="text-base-normal mb-2 me-2 float-right"
              @click="updateTicketType(index, { ...ticketType.value, discounts: onTicketDiscCreate(index) ?? ticketType.value.discounts })"
            >
              {{ $t('organiser.edit_event.tickets_comp.add_discount') }}
            </v-btn>
          </v-card>
        </v-card-text>

        <v-card-title class="text-base-normal mt-4">
          {{ $t('organiser.edit_event.tickets_comp.description') }}
        </v-card-title>
        <v-card-text>
          <v-textarea
            v-model="ticketType.value.description"
            class="text-base-normal"
            :label="$t('organiser.edit_event.tickets_comp.labels.description')"
            :error-messages="errorsStep5[`ticket_types_attributes[${index}].description`]"
            :placeholder="$t('organiser.edit_event.tickets_comp.place_holders.description')"
            required
            @update:model-value="val => {
              updateTicketType(index, { ...ticketType.value, description: val })
              onTicketTypeDescChange(index, val)
            }"
          />
        </v-card-text>
      </v-card>
    </div>
    <v-btn
      color="#1e293b"
      class="text-base-normal mb-4 me-4 float-right"
      @click="addTicketType({
                id: undefined,
                name: '',
                max_amount: 0,
                price: 0,
                discounts: [],
                description: '',
                disabled: false,
              }),
              onTicketTypeCreate()"
    >
      {{ $t('organiser.edit_event.tickets_comp.add_ticket_type') }}
    </v-btn>
  </v-card>
</template>

<style>

</style>
