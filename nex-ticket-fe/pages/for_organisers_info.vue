<script setup lang="ts">
import {
  Agreement01Icon,
  ChartLineData01Icon,
  CheckmarkCircle02Icon,
  CustomerSupportIcon,
  DashboardSpeed01Icon,
  PaymentSuccess02Icon,
  Settings02Icon,
  StartUp02Icon,
  Ticket02Icon,
  TrendUp01Icon,
  UserSharingIcon,
  WavingHand01Icon,
} from 'hugeicons-vue'

const { t } = useI18n()

const signupLink = '/registration'
const contactLink = '/contact'
const faqLink = '/faq'
const contactEmail = '<EMAIL>'
</script>

<template>
  <div class="bg-pie-25 text-slate-800 w-full">
    <!-- Hero Section -->
    <section class="relative w-full px-4 lg:pt-40 md:pt-32 pt-24 lg:pb-20 md:pb-16 pb-12">
      <div class="w-full max-w-[90rem] mx-auto">
        <div class="text-center flex flex-col items-center gap-6 lg:gap-12">
          <!-- Hero Title -->
          <div class="flex flex-col gap-4 lg:gap-6">
            <h1 class="lg:text-8xl md:text-6xl text-4xl font-sofia font-[800] text-pie-700 leading-tight lg:max-w-[60rem] md:max-w-[45rem] max-w-[90vw]">
              {{ t('info.organizerInfo.hero.title') }}
            </h1>
            <p class="lg:text-xl-medium md:text-lg-medium text-base-medium text-slate-700 lg:max-w-[50rem] md:max-w-[40rem] max-w-[85vw] mx-auto">
              {{ t('info.organizerInfo.hero.subtitle') }}
            </p>
          </div>

          <!-- Hero CTAs -->
          <div class="flex md:flex-row flex-col md:gap-6 gap-3 md:w-auto w-full max-w-[90vw]">
            <NexButton
              text-key="info.organizerInfo.hero.ctaPrimary"
              :to="signupLink"
              variant="primary"
              :paddingx="9"
              :paddingy="6"
              :second-border="true"
              border-color="white"
              text-style="lg:text-xl-bold md:text-lg-bold text-base-bold"
              class="md:flex hidden"
            />
            <NexButton
              text-key="info.organizerInfo.hero.ctaSecondary"
              :to="contactLink"
              variant="secondary"
              :paddingx="9"
              :paddingy="6"
              :append-icon="WavingHand01Icon"
              text-style="lg:text-xl-bold md:text-lg-bold text-base-bold"
              class="md:flex hidden"
            />
            <!-- Mobile buttons -->
            <NexButton
              text-key="info.organizerInfo.hero.ctaPrimary"
              :to="signupLink"
              variant="primary"
              :second-border="true"
              border-color="white"
              class="md:hidden flex"
            />
            <NexButton
              text-key="info.organizerInfo.hero.ctaSecondary"
              :to="contactLink"
              variant="secondary"
              :append-icon="WavingHand01Icon"
              class="md:hidden flex"
            />
          </div>

          <!-- Hero Visual -->
          <div class="mt-8 lg:mt-16 w-full max-w-[80rem] mx-auto">
            <div class="bg-gradient-to-br from-pie-100 to-pie-200 rounded-2xl lg:p-12 md:p-8 p-6 shadow-lg border border-pie-300">
              <div class="bg-white rounded-xl lg:p-8 md:p-6 p-4 shadow-sm">
                <div class="text-center text-slate-500 lg:text-lg-medium md:text-base-medium text-sm-medium">
                  🎯 Professional Dashboard Preview
                  <br>
                  <span class="text-xs text-slate-400">Intuitive event management at your fingertips</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Challenge Section -->
    <section class="w-full px-4 lg:py-32 md:py-24 py-16">
      <div class="w-full max-w-[90rem] mx-auto">
        <div class="text-center max-w-[70rem] mx-auto">
          <h2 class="lg:text-6xl md:text-4xl text-3xl font-sofia font-[800] text-slate-900 mb-6 lg:mb-8">
            {{ t('info.organizerInfo.challenge.title') }}
          </h2>
          <p class="lg:text-xl-medium md:text-lg-medium text-base-medium text-slate-600 mb-8 lg:mb-12">
            {{ t('info.organizerInfo.challenge.description') }}
          </p>
          <div class="bg-gradient-to-r from-pie-700 to-pie-500 text-white rounded-2xl lg:p-8 md:p-6 p-4 shadow-lg">
            <p class="lg:text-2xl-bold md:text-xl-bold text-lg-bold">
              {{ t('info.organizerInfo.challenge.punchline') }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Solution Section -->
    <section class="w-full px-4 lg:py-32 md:py-24 py-16 bg-white">
      <div class="w-full max-w-[90rem] mx-auto">
        <div class="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          <!-- Solution Visual -->
          <div class="order-2 lg:order-1">
            <div class="bg-gradient-to-br from-pie-50 to-pie-100 rounded-2xl lg:p-12 md:p-8 p-6 border border-pie-200">
              <div class="bg-white rounded-xl lg:p-8 md:p-6 p-4 shadow-sm">
                <div class="space-y-4">
                  <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-pie-500 rounded-full" />
                    <span class="text-sm-medium text-slate-600">Event Creation</span>
                  </div>
                  <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-pie-400 rounded-full" />
                    <span class="text-sm-medium text-slate-600">Ticket Management</span>
                  </div>
                  <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-pie-300 rounded-full" />
                    <span class="text-sm-medium text-slate-600">Sales Analytics</span>
                  </div>
                  <div class="text-center mt-6 text-slate-500 text-sm-medium">
                    ✨ All-in-one platform visualization
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Solution Content -->
          <div class="order-1 lg:order-2 text-center lg:text-left">
            <h2 class="lg:text-5xl md:text-4xl text-3xl font-sofia font-[800] text-slate-900 mb-6">
              {{ t('info.organizerInfo.solution.title') }}
            </h2>
            <p class="lg:text-xl-medium md:text-lg-medium text-base-medium text-slate-600 mb-8">
              {{ t('info.organizerInfo.solution.description') }}
            </p>

            <!-- Solution Features List -->
            <div class="space-y-4 mb-8">
              <div class="flex items-start gap-3 text-left">
                <CheckmarkCircle02Icon :size="24" class="text-pie-600 mt-1 flex-shrink-0" />
                <span class="text-base-medium text-slate-700">{{ t('info.organizerInfo.solution.listItem1') }}</span>
              </div>
              <div class="flex items-start gap-3 text-left">
                <CheckmarkCircle02Icon :size="24" class="text-pie-600 mt-1 flex-shrink-0" />
                <span class="text-base-medium text-slate-700">{{ t('info.organizerInfo.solution.listItem2') }}</span>
              </div>
              <div class="flex items-start gap-3 text-left">
                <CheckmarkCircle02Icon :size="24" class="text-pie-600 mt-1 flex-shrink-0" />
                <span class="text-base-medium text-slate-700">{{ t('info.organizerInfo.solution.listItem3') }}</span>
              </div>
              <div class="flex items-start gap-3 text-left">
                <CheckmarkCircle02Icon :size="24" class="text-pie-600 mt-1 flex-shrink-0" />
                <span class="text-base-medium text-slate-700">{{ t('info.organizerInfo.solution.listItem4') }}</span>
              </div>
              <div class="flex items-start gap-3 text-left">
                <CheckmarkCircle02Icon :size="24" class="text-pie-600 mt-1 flex-shrink-0" />
                <span class="text-base-medium text-slate-700">{{ t('info.organizerInfo.solution.listItem5') }}</span>
              </div>
            </div>

            <div class="bg-pie-50 rounded-xl p-6 border border-pie-200">
              <p class="lg:text-lg-bold md:text-base-bold text-sm-bold text-pie-700">
                {{ t('info.organizerInfo.solution.punchline') }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="w-full px-4 lg:py-32 md:py-24 py-16 bg-pie-25">
      <div class="w-full max-w-[90rem] mx-auto">
        <div class="text-center mb-16 lg:mb-20">
          <h2 class="lg:text-6xl md:text-4xl text-3xl font-sofia font-[800] text-slate-900 mb-6">
            {{ t('info.organizerInfo.benefits.title') }}
          </h2>
        </div>

        <div class="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-8 lg:gap-12">
          <!-- Setup Benefit -->
          <div class="bg-white rounded-2xl lg:p-8 md:p-6 p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div class="flex flex-col items-center text-center gap-4">
              <div class="w-16 h-16 bg-gradient-to-br from-pie-500 to-pie-600 rounded-2xl flex items-center justify-center">
                <StartUp02Icon :size="32" class="text-white" />
              </div>
              <h3 class="lg:text-xl-bold md:text-lg-bold text-base-bold text-slate-900">
                {{ t('info.organizerInfo.benefits.setup.title') }}
              </h3>
              <p class="text-base-medium text-slate-600">
                {{ t('info.organizerInfo.benefits.setup.description') }}
              </p>
            </div>
          </div>

          <!-- Data Benefit -->
          <div class="bg-white rounded-2xl lg:p-8 md:p-6 p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div class="flex flex-col items-center text-center gap-4">
              <div class="w-16 h-16 bg-gradient-to-br from-pie-500 to-pie-600 rounded-2xl flex items-center justify-center">
                <ChartLineData01Icon :size="32" class="text-white" />
              </div>
              <h3 class="lg:text-xl-bold md:text-lg-bold text-base-bold text-slate-900">
                {{ t('info.organizerInfo.benefits.data.title') }}
              </h3>
              <p class="text-base-medium text-slate-600">
                {{ t('info.organizerInfo.benefits.data.description') }}
              </p>
            </div>
          </div>

          <!-- Pricing Benefit -->
          <div class="bg-white rounded-2xl lg:p-8 md:p-6 p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div class="flex flex-col items-center text-center gap-4">
              <div class="w-16 h-16 bg-gradient-to-br from-pie-500 to-pie-600 rounded-2xl flex items-center justify-center">
                <PaymentSuccess02Icon :size="32" class="text-white" />
              </div>
              <h3 class="lg:text-xl-bold md:text-lg-bold text-base-bold text-slate-900">
                {{ t('info.organizerInfo.benefits.pricing.title') }}
              </h3>
              <p class="text-base-medium text-slate-600">
                {{ t('info.organizerInfo.benefits.pricing.description') }}
              </p>
            </div>
          </div>

          <!-- Attendee Benefit -->
          <div class="bg-white rounded-2xl lg:p-8 md:p-6 p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div class="flex flex-col items-center text-center gap-4">
              <div class="w-16 h-16 bg-gradient-to-br from-pie-500 to-pie-600 rounded-2xl flex items-center justify-center">
                <Ticket02Icon :size="32" class="text-white" />
              </div>
              <h3 class="lg:text-xl-bold md:text-lg-bold text-base-bold text-slate-900">
                {{ t('info.organizerInfo.benefits.attendee.title') }}
              </h3>
              <p class="text-base-medium text-slate-600">
                {{ t('info.organizerInfo.benefits.attendee.description') }}
              </p>
            </div>
          </div>

          <!-- Customizable Benefit -->
          <div class="bg-white rounded-2xl lg:p-8 md:p-6 p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div class="flex flex-col items-center text-center gap-4">
              <div class="w-16 h-16 bg-gradient-to-br from-pie-500 to-pie-600 rounded-2xl flex items-center justify-center">
                <Settings02Icon :size="32" class="text-white" />
              </div>
              <h3 class="lg:text-xl-bold md:text-lg-bold text-base-bold text-slate-900">
                {{ t('info.organizerInfo.benefits.customizable.title') }}
              </h3>
              <p class="text-base-medium text-slate-600">
                {{ t('info.organizerInfo.benefits.customizable.description') }}
              </p>
            </div>
          </div>

          <!-- Support Benefit -->
          <div class="bg-white rounded-2xl lg:p-8 md:p-6 p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div class="flex flex-col items-center text-center gap-4">
              <div class="w-16 h-16 bg-gradient-to-br from-pie-500 to-pie-600 rounded-2xl flex items-center justify-center">
                <CustomerSupportIcon :size="32" class="text-white" />
              </div>
              <h3 class="lg:text-xl-bold md:text-lg-bold text-base-bold text-slate-900">
                {{ t('info.organizerInfo.benefits.support.title') }}
              </h3>
              <p class="text-base-medium text-slate-600">
                {{ t('info.organizerInfo.benefits.support.description') }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Reporting Section -->
    <section class="w-full px-4 lg:py-32 md:py-24 py-16 bg-gradient-to-br from-pie-700 to-pie-800 text-white">
      <div class="w-full max-w-[90rem] mx-auto">
        <div class="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          <!-- Reporting Content -->
          <div class="text-center lg:text-left">
            <h2 class="lg:text-5xl md:text-4xl text-3xl font-sofia font-[800] mb-6">
              {{ t('info.organizerInfo.reporting.title') }}
            </h2>
            <p class="lg:text-xl-medium md:text-lg-medium text-base-medium text-pie-100 mb-8">
              {{ t('info.organizerInfo.reporting.description') }}
            </p>

            <!-- Reporting Features -->
            <div class="space-y-4 mb-8">
              <div class="flex items-start gap-3 text-left">
                <div class="w-6 h-6 bg-pie-300 rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                  <div class="w-2 h-2 bg-pie-700 rounded-full" />
                </div>
                <span class="text-base-medium text-pie-50">{{ t('info.organizerInfo.reporting.listItem1') }}</span>
              </div>
              <div class="flex items-start gap-3 text-left">
                <div class="w-6 h-6 bg-pie-300 rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                  <div class="w-2 h-2 bg-pie-700 rounded-full" />
                </div>
                <span class="text-base-medium text-pie-50">{{ t('info.organizerInfo.reporting.listItem2') }}</span>
              </div>
              <div class="flex items-start gap-3 text-left">
                <div class="w-6 h-6 bg-pie-300 rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                  <div class="w-2 h-2 bg-pie-700 rounded-full" />
                </div>
                <span class="text-base-medium text-pie-50">{{ t('info.organizerInfo.reporting.listItem3') }}</span>
              </div>
              <div class="flex items-start gap-3 text-left">
                <div class="w-6 h-6 bg-pie-300 rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                  <div class="w-2 h-2 bg-pie-700 rounded-full" />
                </div>
                <span class="text-base-medium text-pie-50">{{ t('info.organizerInfo.reporting.listItem4') }}</span>
              </div>
            </div>
          </div>

          <!-- Reporting Visual -->
          <div class="order-first lg:order-last">
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl lg:p-8 md:p-6 p-4 border border-white/20">
              <div class="bg-white rounded-xl lg:p-6 md:p-4 p-3 shadow-lg">
                <div class="space-y-4">
                  <div class="flex items-center justify-between">
                    <span class="text-sm-bold text-slate-700">Analytics Dashboard</span>
                    <DashboardSpeed01Icon :size="20" class="text-pie-600" />
                  </div>
                  <div class="grid grid-cols-2 gap-4">
                    <div class="bg-pie-50 rounded-lg p-3">
                      <div class="text-xs text-slate-500">
                        Total Sales
                      </div>
                      <div class="text-lg-bold text-pie-700">
                        €2,450
                      </div>
                    </div>
                    <div class="bg-pie-50 rounded-lg p-3">
                      <div class="text-xs text-slate-500">
                        Tickets Sold
                      </div>
                      <div class="text-lg-bold text-pie-700">
                        156
                      </div>
                    </div>
                  </div>
                  <div class="h-20 bg-gradient-to-r from-pie-100 to-pie-200 rounded-lg flex items-end justify-center">
                    <span class="text-xs text-slate-500 mb-2">📊 Real-time insights</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="w-full px-4 lg:py-32 md:py-24 py-16 bg-white">
      <div class="w-full max-w-[90rem] mx-auto">
        <div class="text-center mb-16 lg:mb-20">
          <h2 class="lg:text-6xl md:text-4xl text-3xl font-sofia font-[800] text-slate-900 mb-6">
            {{ t('info.organizerInfo.testimonials.title') }}
          </h2>
        </div>

        <div class="grid lg:grid-cols-2 gap-8 lg:gap-12">
          <!-- Testimonial 1 -->
          <div class="bg-pie-25 rounded-2xl lg:p-8 md:p-6 p-6 border border-pie-200">
            <div class="flex flex-col gap-6">
              <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-pie-500 to-pie-600 rounded-full flex items-center justify-center">
                  <UserSharingIcon :size="24" class="text-white" />
                </div>
                <div>
                  <div class="text-base-bold text-slate-900">
                    Event Organizer
                  </div>
                  <div class="text-sm-medium text-slate-600">
                    Music Festival
                  </div>
                </div>
              </div>
              <blockquote class="text-lg-medium text-slate-700 italic">
                "{{ t('info.organizerInfo.testimonials.quote1') }}"
              </blockquote>
            </div>
          </div>

          <!-- Testimonial 2 -->
          <div class="bg-pie-25 rounded-2xl lg:p-8 md:p-6 p-6 border border-pie-200">
            <div class="flex flex-col gap-6">
              <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-br from-pie-500 to-pie-600 rounded-full flex items-center justify-center">
                  <UserSharingIcon :size="24" class="text-white" />
                </div>
                <div>
                  <div class="text-base-bold text-slate-900">
                    Event Manager
                  </div>
                  <div class="text-sm-medium text-slate-600">
                    Corporate Events
                  </div>
                </div>
              </div>
              <blockquote class="text-lg-medium text-slate-700 italic">
                "{{ t('info.organizerInfo.testimonials.quote2') }}"
              </blockquote>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="w-full px-4 lg:py-40 md:py-32 py-24 bg-gradient-to-br from-pie-25 to-pie-50">
      <div class="w-full max-w-[90rem] mx-auto text-center">
        <div class="max-w-[70rem] mx-auto">
          <h2 class="lg:text-7xl md:text-5xl text-4xl font-sofia font-[800] text-slate-900 mb-6 lg:mb-8">
            {{ t('info.organizerInfo.cta.title') }}
          </h2>
          <p class="lg:text-xl-medium md:text-lg-medium text-base-medium text-slate-600 mb-12 lg:mb-16 max-w-[50rem] mx-auto">
            {{ t('info.organizerInfo.cta.description') }}
          </p>

          <!-- CTA Buttons -->
          <div class="flex md:flex-row flex-col md:gap-6 gap-4 justify-center items-center md:w-auto w-full max-w-[90vw] mx-auto">
            <NexButton
              text-key="info.organizerInfo.cta.buttonPrimary"
              :to="signupLink"
              variant="primary"
              :paddingx="9"
              :paddingy="6"
              :second-border="true"
              border-color="white"
              text-style="lg:text-xl-bold md:text-lg-bold text-base-bold"
              class="md:flex hidden"
            />
            <NexButton
              text-key="info.organizerInfo.cta.buttonSecondary"
              :to="contactLink"
              variant="secondary"
              :paddingx="9"
              :paddingy="6"
              :append-icon="WavingHand01Icon"
              text-style="lg:text-xl-bold md:text-lg-bold text-base-bold"
              class="md:flex hidden"
            />
            <!-- Mobile buttons -->
            <NexButton
              text-key="info.organizerInfo.cta.buttonPrimary"
              :to="signupLink"
              variant="primary"
              :second-border="true"
              border-color="white"
              class="md:hidden flex"
            />
            <NexButton
              text-key="info.organizerInfo.cta.buttonSecondary"
              :to="contactLink"
              variant="secondary"
              :append-icon="WavingHand01Icon"
              class="md:hidden flex"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="w-full px-4 lg:py-32 md:py-24 py-16 bg-white">
      <div class="w-full max-w-[90rem] mx-auto text-center">
        <div class="max-w-[50rem] mx-auto">
          <h2 class="lg:text-5xl md:text-4xl text-3xl font-sofia font-[800] text-slate-900 mb-6">
            {{ t('info.organizerInfo.contact.title') }}
          </h2>
          <p class="lg:text-xl-medium md:text-lg-medium text-base-medium text-slate-600 mb-12">
            {{ t('info.organizerInfo.contact.description') }}
          </p>

          <!-- Contact Links -->
          <div class="flex flex-col md:flex-row md:gap-8 gap-6 justify-center items-center">
            <a
              :href="`mailto:${contactEmail}`"
              class="flex items-center gap-3 text-pie-600 hover:text-pie-700 transition-colors duration-200 text-lg-medium hover:underline"
            >
              <div class="w-8 h-8 bg-pie-100 rounded-lg flex items-center justify-center">
                📧
              </div>
              {{ contactEmail }}
            </a>
            <NuxtLinkLocale
              :to="contactLink"
              class="flex items-center gap-3 text-pie-600 hover:text-pie-700 transition-colors duration-200 text-lg-medium hover:underline"
            >
              <div class="w-8 h-8 bg-pie-100 rounded-lg flex items-center justify-center">
                📝
              </div>
              {{ t('info.organizerInfo.contact.linkContactForm') }}
            </NuxtLinkLocale>
            <NuxtLinkLocale
              :to="faqLink"
              class="flex items-center gap-3 text-pie-600 hover:text-pie-700 transition-colors duration-200 text-lg-medium hover:underline"
            >
              <div class="w-8 h-8 bg-pie-100 rounded-lg flex items-center justify-center">
                ❓
              </div>
              {{ t('info.organizerInfo.contact.linkFAQ') }}
            </NuxtLinkLocale>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
