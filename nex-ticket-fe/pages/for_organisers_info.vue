<script setup lang="ts">
const { t } = useI18n()

const signupLink = '/signup'
const contactLink = '/contact'
const faqLink = '/faq'
const contactEmail = '<EMAIL>'
</script>

<template>
  <div class="bg-white text-slate-800">
    <section class="bg-gradient-to-br from-slate-50 to-slate-100 py-20 md:py-28 text-center">
      <div class="container mx-auto px-4 md:px-6">
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 mb-4 leading-tight">
          {{ t('info.organizerInfo.hero.title') }}
        </h1>
        <p class="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto mb-8">
          {{ t('info.organizerInfo.hero.subtitle') }}
        </p>
        <div class="flex flex-col sm:flex-row justify-center items-center gap-4">
          <NuxtLinkLocale
            :to="signupLink"
            class="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold px-8 py-3 rounded-md transition duration-300 shadow-md w-full sm:w-auto"
          >
            {{ t('info.organizerInfo.hero.ctaPrimary') }}
          </NuxtLinkLocale>
          <NuxtLinkLocale
            :to="contactLink"
            class="inline-block bg-white hover:bg-slate-50 text-indigo-600 border border-indigo-600 font-semibold px-8 py-3 rounded-md transition duration-300 w-full sm:w-auto"
          >
            {{ t('info.organizerInfo.hero.ctaSecondary') }}
          </NuxtLinkLocale>
        </div>
        <div class="mt-12 text-slate-500">
          [Placeholder for compelling image/video showing platform ease of use]
        </div>
      </div>
    </section>

    <section class="py-16 md:py-24">
      <div class="container mx-auto px-4 md:px-6 text-center max-w-4xl">
        <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
          {{ t('info.organizerInfo.challenge.title') }}
        </h2>
        <p class="text-lg text-slate-600 mb-8">
          {{ t('info.organizerInfo.challenge.description') }}
        </p>
        <p class="text-xl font-semibold text-indigo-700">
          {{ t('info.organizerInfo.challenge.punchline') }}
        </p>
      </div>
    </section>

    <section class="py-16 md:py-24 bg-slate-50">
      <div class="container mx-auto px-4 md:px-6 grid md:grid-cols-2 gap-12 items-center">
        <div>
          <div class="mb-8 md:mb-0 text-center text-slate-500">
            [Placeholder for an image illustrating the solution/success]
          </div>
        </div>
        <div class="text-center md:text-left">
          <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
            {{ t('info.organizerInfo.solution.title') }}
          </h2>
          <p class="text-lg text-slate-600 mb-6">
            {{ t('info.organizerInfo.solution.description') }}
          </p>
          <ul class="space-y-2 text-slate-600 text-left list-disc list-inside marker:text-indigo-500">
            <li>{{ t('info.organizerInfo.solution.listItem1') }}</li>
            <li>{{ t('info.organizerInfo.solution.listItem2') }}</li>
            <li>{{ t('info.organizerInfo.solution.listItem3') }}</li>
            <li>{{ t('info.organizerInfo.solution.listItem4') }}</li>
            <li>{{ t('info.organizerInfo.solution.listItem5') }}</li>
          </ul>
          <p class="text-lg font-semibold text-slate-700 mt-6">
            {{ t('info.organizerInfo.solution.punchline') }}
          </p>
        </div>
      </div>
    </section>

    <section class="py-16 md:py-24">
      <div class="container mx-auto px-4 md:px-6">
        <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-12 text-center">
          {{ t('info.organizerInfo.benefits.title') }}
        </h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-12">
          <div class="text-center p-6 bg-slate-50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
            <div class="text-4xl text-indigo-600 mb-4 inline-block">
              🚀
            </div> <h3 class="text-xl font-semibold text-slate-800 mb-2">
              {{ t('info.organizerInfo.benefits.setup.title') }}
            </h3>
            <p class="text-slate-600">
              {{ t('info.organizerInfo.benefits.setup.description') }}
            </p>
          </div>
          <div class="text-center p-6 bg-slate-50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
            <div class="text-4xl text-indigo-600 mb-4 inline-block">
              📊
            </div> <h3 class="text-xl font-semibold text-slate-800 mb-2">
              {{ t('info.organizerInfo.benefits.data.title') }}
            </h3>
            <p class="text-slate-600">
              {{ t('info.organizerInfo.benefits.data.description') }}
            </p>
          </div>
          <div class="text-center p-6 bg-slate-50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
            <div class="text-4xl text-indigo-600 mb-4 inline-block">
              💰
            </div> <h3 class="text-xl font-semibold text-slate-800 mb-2">
              {{ t('info.organizerInfo.benefits.pricing.title') }}
            </h3>
            <p class="text-slate-600">
              {{ t('info.organizerInfo.benefits.pricing.description') }}
            </p>
          </div>
          <div class="text-center p-6 bg-slate-50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
            <div class="text-4xl text-indigo-600 mb-4 inline-block">
              🎟️
            </div> <h3 class="text-xl font-semibold text-slate-800 mb-2">
              {{ t('info.organizerInfo.benefits.attendee.title') }}
            </h3>
            <p class="text-slate-600">
              {{ t('info.organizerInfo.benefits.attendee.description') }}
            </p>
          </div>
          <div class="text-center p-6 bg-slate-50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
            <div class="text-4xl text-indigo-600 mb-4 inline-block">
              ⚙️
            </div> <h3 class="text-xl font-semibold text-slate-800 mb-2">
              {{ t('info.organizerInfo.benefits.customizable.title') }}
            </h3>
            <p class="text-slate-600">
              {{ t('info.organizerInfo.benefits.customizable.description') }}
            </p>
          </div>
          <div class="text-center p-6 bg-slate-50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
            <div class="text-4xl text-indigo-600 mb-4 inline-block">
              🤝
            </div> <h3 class="text-xl font-semibold text-slate-800 mb-2">
              {{ t('info.organizerInfo.benefits.support.title') }}
            </h3>
            <p class="text-slate-600">
              {{ t('info.organizerInfo.benefits.support.description') }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <section class="py-16 md:py-24 bg-indigo-700 text-white">
      <div class="container mx-auto px-4 md:px-6 grid md:grid-cols-2 gap-12 items-center">
        <div class="text-center md:text-left">
          <h2 class="text-3xl md:text-4xl font-bold mb-4">
            {{ t('info.organizerInfo.reporting.title') }}
          </h2>
          <p class="text-lg text-indigo-100 mb-6">
            {{ t('info.organizerInfo.reporting.description') }}
          </p>
          <ul class="space-y-2 text-indigo-50 text-left list-disc list-inside marker:text-indigo-300">
            <li>{{ t('info.organizerInfo.reporting.listItem1') }}</li>
            <li>{{ t('info.organizerInfo.reporting.listItem2') }}</li>
            <li>{{ t('info.organizerInfo.reporting.listItem3') }}</li>
            <li>{{ t('info.organizerInfo.reporting.listItem4') }}</li>
          </ul>
        </div>
        <div>
          <div class="mt-8 md:mt-0 text-center">
            <div class="bg-white/10 p-6 rounded-lg text-indigo-100">
              [Placeholder for Reporting Dashboard Screenshot]
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="py-20 md:py-28 text-center bg-slate-100">
      <div class="container mx-auto px-4 md:px-6">
        <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
          {{ t('info.organizerInfo.cta.title') }}
        </h2>
        <p class="text-lg text-slate-600 max-w-2xl mx-auto mb-8">
          {{ t('info.organizerInfo.cta.description') }}
        </p>
        <div class="flex flex-col sm:flex-row justify-center items-center gap-4">
          <NuxtLinkLocale
            :to="signupLink"
            class="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold px-8 py-3 rounded-md transition duration-300 shadow-md w-full sm:w-auto"
          >
            {{ t('info.organizerInfo.cta.buttonPrimary') }}
          </NuxtLinkLocale>
          <NuxtLinkLocale
            :to="contactLink"
            class="inline-block bg-white hover:bg-slate-50 text-indigo-600 border border-indigo-600 font-semibold px-8 py-3 rounded-md transition duration-300 w-full sm:w-auto"
          >
            {{ t('info.organizerInfo.cta.buttonSecondary') }}
          </NuxtLinkLocale>
        </div>
      </div>
    </section>

    <section class="py-16 md:py-24">
      <div class="container mx-auto px-4 md:px-6 text-center max-w-3xl">
        <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
          {{ t('info.organizerInfo.contact.title') }}
        </h2>
        <p class="text-lg text-slate-600 mb-8">
          {{ t('info.organizerInfo.contact.description') }}
        </p>
        <div class="space-y-4 md:space-y-0 md:space-x-8 flex flex-col md:flex-row justify-center items-center">
          <a :href="`mailto:${contactEmail}`" class="text-indigo-600 hover:text-indigo-800 hover:underline text-lg font-medium">
            📧 {{ contactEmail }} </a>
          <NuxtLinkLocale :to="contactLink" class="text-indigo-600 hover:text-indigo-800 hover:underline text-lg font-medium">
            📝 {{ t('info.organizerInfo.contact.linkContactForm') }}
          </NuxtLinkLocale>
          <NuxtLinkLocale :to="faqLink" class="text-indigo-600 hover:text-indigo-800 hover:underline text-lg font-medium">
            ❓ {{ t('info.organizerInfo.contact.linkFAQ') }}
          </NuxtLinkLocale>
        </div>
      </div>
    </section>
  </div>
</template>
