<script lang="ts" setup>
import { ArcElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, LineElement, PointElement, Title, Tooltip } from 'chart.js'
import { Doughnut, Line } from 'vue-chartjs'
import { useFeedback } from '~/composables/useFeedback'

ChartJS.register(Title, Tooltip, Legend, ArcElement, CategoryScale, LinearScale, PointElement, LineElement)

const { t } = useI18n()
const router = useRouter()
const feedback = useFeedback()

interface ReportData {
  total_revenue: number
  daily_sales: Array<{ date: string, total: number }>
  events_breakdown: Array<{ id: number, name: string, total: number }>
}

interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string[]
    borderColor?: string
  }>
}

const stats = ref<ReportData | null>(null)
const { pageLoading } = storeToRefs(useGeneralStore())
const error = ref<string | null>(null)
const dateRange = ref({
  start: null as string | null,
  end: null as string | null,
})

const chartColors = [
  '#3B82F6',
  '#10B981',
  '#F59E0B',
  '#EF4444',
  '#8B5CF6',
  '#06B6D4',
  '#EC4899',
  '#F97316',
  '#84CC16',
  '#64748B',
]

const mainStats = computed(() => stats.value
  ? [
      {
        title: t('organiser.reporting.organiser.total_revenue'),
        value: stats.value.total_revenue.toLocaleString('en-US', { style: 'currency', currency: 'EUR' }),
        icon: '💰',
        trend: t('organiser.reporting.organiser.trend'),
      },
    ]
  : [])

const salesChartData = computed<ChartData>(() => ({
  labels: stats.value?.daily_sales.map(d => new Date(d.date).toLocaleDateString()) || [],
  datasets: [{
    label: t('organiser.reporting.organiser.daily_sales'),
    data: stats.value?.daily_sales.map(d => d.total) || [],
    borderColor: '#3B82F6',
    tension: 0.4,
  }],
}))

const eventBreakdownChartData = computed<ChartData>(() => ({
  labels: stats.value?.events_breakdown.map(e => e.name) || [],
  datasets: [{
    label: t('organiser.reporting.organiser.event_revenue'),
    data: stats.value?.events_breakdown.map(e => e.total) || [],
    backgroundColor: chartColors,
  }],
}))

async function fetchStats() {
  pageLoading.value = true
  error.value = null

  try {
    const query = {
      start_date: dateRange.value.start,
      end_date: dateRange.value.end,
      include_pending: true,
    }

    const { data: fetchedStats, error: fetchError } = await useAPI(
      '/api/organiser/reporting/organiser',
      { query },
    )

    if (fetchError.value) {
      feedback.error(t('errors.reporting_error'), { level: 'error', rollbar: true, extras: fetchError.value })
      error.value = fetchError.value.message
    }
    else if (fetchedStats.value) {
      stats.value = fetchedStats.value as ReportData

      if (isDataEmpty(stats.value)) {
        feedback.info(t('organiser.reporting.organiser.no_data'))
      }
    }
    else {
      stats.value = null
      feedback.error(t('organiser.reporting.organiser.unexpected_empty_response'), { level: 'warning', rollbar: true })
    }
  }
  catch (err: any) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
    error.value = err.message // Use actual error message instead of generic text
  }
  finally {
    pageLoading.value = false
  }
}
function isDataEmpty(data: ReportData | null): boolean {
  if (!data)
    return true
  return !data.total_revenue && data.daily_sales.length === 0 && data.events_breakdown.length === 0
}

function viewEventReport(eventId: number) {
  router.push(`/organiser/reporting/events/${eventId}`)
}

function clearDateRange() {
  dateRange.value.start = null
  dateRange.value.end = null
}

watch(() => dateRange.value, fetchStats, { deep: true })

onMounted(fetchStats)
</script>

<template>
  <div class="mx-auto px-4 py-4 md:max-w-7xl md:px-4 md:lg:px-8 md:py-8">
    <div class="flex justify-between items-center mb-4 md:mb-8">
      <div class="text-xl-bold text-slate-800 md:text-4xl-bold">
        {{ $t('organiser.reporting.organiser.organiser_report') }}
      </div>
      <button
        class="transition-basic bg-pie-800 text-white text-base-medium px-4 py-2 rounded-lg hover:bg-pie-700 md:text-lg-medium md:px-6 md:py-3"
        @click="clearDateRange"
      >
        {{ $t('organiser.reporting.organiser.show_all_data') }}
      </button>
    </div>

    <div class="bg-slate-100 shadow-pie-950/50 shadow-sm p-4 rounded-lg mb-4 border border-slate-200 md:p-6 md:mb-8">
      <div class="grid grid-cols-1 gap-4 md:flex md:items-center">
        <div class="w-full md:flex-1">
          <label class="block text-base-medium text-slate-600 mb-1 md:text-lg-medium">{{ $t('organiser.reporting.organiser.start_date') }}</label>
          <input
            v-model="dateRange.start"
            type="date"
            class="p-2 border border-slate-900 rounded-md w-full md:border-2 text-sm-medium"
          >
        </div>
        <div class="w-full md:flex-1">
          <label class="block text-base-medium text-slate-600 mb-1 md:text-lg-medium">{{ $t('organiser.reporting.organiser.end_date') }}</label>
          <input
            v-model="dateRange.end"
            type="date"
            class="p-2 border rounded-md w-full md:border-2 text-sm-medium"
          >
        </div>
      </div>
    </div>

    <div v-if="pageLoading" class="space-y-6 md:space-y-8">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3 md:gap-6">
        <div
          v-for="index in 3"
          :key="index"
          class="p-4 rounded-xl shadow-pie-950/20 shadow-md border border-slate-200 bg-slate-100 md:p-6"
        >
          <div class="flex items-center justify-between">
            <div class="flex flex-col gap-3 pb-1">
              <div class="placeholder animate-pulse h-5 w-32" />
              <div class="placeholder animate-pulse h-7 w-20" />
            </div>
            <div class="placeholder-circle size-12 animate-pulse" />
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-6 md:lg:grid-cols-2 md:gap-8">
        <div class="p-4 bg-slate-100 rounded-xl shadow-pie-950/20 shadow-md border border-slate-300 md:p-6">
          <div class="text-xl-bold text-slate-900 mb-4 md:text-2xl-bold">
            {{ $t('organiser.reporting.organiser.sales_trend') }}
          </div>
          <div class="flex first-letter:h-64 md:h-80 h-60 items-center justify-between px-10">
            <div class="placeholder-circle size-24 animate-pulse" />
            <div class="placeholder-circle size-20 animate-pulse" />
            <div class="placeholder-circle size-16 animate-pulse" />
          </div>
        </div>

        <div class="p-4 bg-slate-100 shadow-pie-950/20 shadow-md rounded-xl border border-slate-300 md:p-6">
          <div class="text-xl-bold text-slate-900 mb-4 md:text-2xl-bold">
            {{ $t('organiser.reporting.organiser.event_revenue_breakdown') }}
          </div>
          <div class="flex h-64 md:h-80 items-center justify-center">
            <div class="flex placeholder-circle animate-pulse md:size-72 size-56 items-center justify-center">
              <div class="z-10 placeholder-circle-slate-100 md:size-36 size-28 animate-pulse bg-slate-100" />
            </div>
          </div>
        </div>
      </div>

      <div class="bg-slate-300 rounded-xl shadow-pie-950/20 shadow-2xl border border-slate-300 overflow-x-auto">
        <div class="p-4 border-b border-gray-200 md:p-6">
          <h3 class="text-xl-bold md:text-2xl-bold">
            {{ $t('organiser.reporting.organiser.events_breakdown') }}
          </h3>
        </div>
        <table class="w-full">
          <thead class="bg-slate-200 shadow-pie-700">
            <tr>
              <th class="px-4 py-2 text-left text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.organiser.event') }}
              </th>
              <th class="px-4 py-2 text-right text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.organiser.total_revenue') }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="index in 5"
              :key="index"
              class="bg-slate-100 hover:bg-slate-50 cursor-pointer"
            >
              <td class="px-4 py-2 text-sm-normal text-slate-800 md:px-6 md:py-4 md:text-sm-normal">
                <div>
                  <div class="placeholder animate-pulse h-6 md:h-5 w-32" />
                </div>
              </td>
              <td class="px-4 py-2 text-right md:px-6 md:py-4 float-right">
                <div class="placeholder animate-pulse h-6 md:h-5 w-20" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div v-else-if="stats" class="space-y-6 md:space-y-8">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3 md:gap-6">
        <div
          v-for="stat in mainStats"
          :key="stat.title"
          class="p-4 rounded-xl shadow-pie-950/20 shadow-md border border-slate-200 bg-slate-100 md:p-6"
        >
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base-normal text-slate-600 mb-1 md:text-lg-normal">
                {{ stat.title }}
              </p>
              <p class="text-xl-bold text-slate-900 md:text-2xl-bold">
                {{ stat.value }}
              </p>
            </div>
            <span class="text-2xl md:text-3xl">{{ stat.icon }}</span>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-6 md:lg:grid-cols-2 md:gap-8">
        <div class="p-4 bg-slate-100 rounded-xl shadow-pie-950/20 shadow-md border border-slate-300 md:p-6">
          <div class="text-xl-bold text-slate-900 mb-4 md:text-2xl-bold">
            {{ $t('organiser.reporting.organiser.sales_trend') }}
          </div>
          <div class="h-64 md:h-80">
            <Line
              :data="salesChartData"
              :options="{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  title: {
                    display: false,
                  },
                  legend: {
                    labels: {
                      font: {
                        family: 'Onest',
                        weight: 'bold',
                      },
                    },
                  },
                  tooltip: {
                    titleFont: {
                      family: 'Onest',
                      weight: 'bold',
                    },
                    bodyFont: {
                      family: 'Onest',
                      weight: 'bold',
                    },
                  },
                },
                scales: {
                  x: {
                    ticks: {
                      font: {
                        family: 'Onest',
                        size: 14,
                        weight: 'normal',
                      },
                    },
                  },
                  y: {
                    ticks: {
                      font: {
                        family: 'Onest',
                        size: 14,
                        weight: 'normal',
                      },
                    },
                  },
                },
              }"
            />
          </div>
        </div>

        <div class="p-4 bg-slate-100 shadow-pie-950/20 shadow-md rounded-xl border border-slate-300 md:p-6">
          <div class="text-xl-bold text-slate-900 mb-4 md:text-2xl-bold">
            {{ $t('organiser.reporting.organiser.event_revenue_breakdown') }}
          </div>
          <div class="h-64 md:h-80">
            <Doughnut
              :data="eventBreakdownChartData"
              :options="{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  title: {
                    display: false,
                  },
                  legend: {
                    position: 'bottom',
                    labels: {
                      font: {
                        family: 'Onest',
                        size: 12,
                        weight: 'bold',
                      },
                    },
                  },
                  tooltip: {
                    borderWidth: 1,
                    titleFont: {
                      family: 'Onest',
                      size: 16,
                    },
                    bodyFont: {
                      family: 'Onest',
                      size: 14,
                    },
                  },
                },
              }"
            />
          </div>
        </div>
      </div>

      <div class="bg-slate-300 rounded-xl shadow-pie-950/20 shadow-2xl border border-slate-300 overflow-x-auto">
        <div class="p-4 border-b border-gray-200 md:p-6">
          <h3 class="text-xl-bold md:text-2xl-bold">
            {{ $t('organiser.reporting.organiser.events_breakdown') }}
          </h3>
        </div>
        <table class="w-full">
          <thead class="bg-slate-200 shadow-pie-700">
            <tr>
              <th class="px-4 py-2 text-left text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.organiser.event') }}
              </th>
              <th class="px-4 py-2 text-right text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.organiser.total_revenue') }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="event in stats.events_breakdown"
              :key="event.id"
              class="bg-slate-100 hover:bg-slate-50 cursor-pointer"
              @click="viewEventReport(event.id)"
            >
              <td class="px-4 py-2 text-sm-normal text-slate-800 md:px-6 md:py-4 md:text-sm-normal">
                <NuxtLinkLocale
                  :to="`/organiser/reporting/event/${event.id}`"
                  class="text-base-normal text-slate-600 md:text-base-normal"
                >
                  {{ event.name }}
                </NuxtLinkLocale>
              </td>
              <td class="px-4 py-2 text-sm-normal text-slate-800 text-right md:px-6 md:py-4 md:text-base-normal">
                {{ event.total.toLocaleString('en-US', { style: 'currency', currency: 'EUR' }) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- insurence of information for user if stats are for some reason empty (should never happen) -->
    <div
      v-else
      class="md:p-4 md:text-2xl-medium
    p-3 text-red-500 text-center text-xl-medium"
    >
      {{ $t('organiser.reporting.event.no_data_loaded_info') }}
    </div>
  </div>
</template>
