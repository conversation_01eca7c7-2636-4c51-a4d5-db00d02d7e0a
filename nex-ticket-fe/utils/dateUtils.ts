import { format } from 'date-fns'

export function formatDateToDate(date: Date): string {
  return format(date, 'EEE, dd MMM yyyy')
}

export function formatDateToTime(date: Date): string {
  return format(date, 'HH:mm')
}

export function toLocalISOString(date: Date): string {
  const offsetMs = date.getTimezoneOffset() * 60 * 1000
  const localTime = new Date(date.getTime() - offsetMs)
  return localTime.toISOString()
}
