import { useFeedback } from '~/composables/useFeedback'

export default defineNuxtPlugin((nuxtApp) => {
  const authStore = useAuthStore(useNuxtApp().$pinia)
  const { token } = storeToRefs(authStore)
  const visitorToken = useCookie('visitor-token')
  const feedback = useFeedback()

  const api = $fetch.create({
    onRequest({ options }) {
      if (token.value) {
        options.headers = options.headers || {}
        options.headers.set('Authorization', token.value)
      }

      // @ts-expect-error Typescript is not handling the autoimports correctly
      const locale = nuxtApp.$i18n.locale.value

      if (locale) {
        options.headers = options.headers || {}
        options.headers.set('Accept-Language', locale)
      }

      // Add visitor token to request headers
      if (visitorToken.value) {
        options.headers = options.headers || {}
        options.headers.set('visitor-token', visitorToken.value)
      }
    },
    async onResponse({ response }) {
      // Write visitor token
      const newVisitorToken = response.headers.get('visitor-token')
      if (newVisitorToken && newVisitorToken !== visitorToken.value) {
        visitorToken.value = newVisitorToken
      }

      const newToken = response.headers.get('Authorization')
      if (newToken && newToken !== token.value) {
        authStore.setToken(newToken)
      }
    },
    async onResponseError({ response }) {
      if (response._data?.error) {
        feedback.error(response._data?.error, { level: 'error', rollbar: true })
        return
      }
      feedback.error(nuxtApp.$i18n.t('errors.parse_error'), { level: 'error', rollbar: true })
      const errorMessage = response._data?.error || response._data?.errors?.[0]
      if (response.status === 401) {
        await nuxtApp.runWithContext(() => navigateToWLocale(response.url.includes('/organiser') ? '/organiser/login' : '/login'))
      }
      else {
        feedback.error('API Error:', { level: 'error', rollbar: true, extras: [response.status, response.statusText] })
      }

      if (errorMessage) {
        toast.error(errorMessage)
        return
      }
      toast.error(nuxtApp.$i18n.t('errors.parse_error'))
    },
  })

  return {
    provide: {
      api,
    },
  }
})
