import type { I18nTranslation } from '@/utils/constants'
import { date } from 'yup'
import Forgot_password from '~/pages/forgot_password.vue'

export default {
  nav: {
    events: 'Wydarzenia',
    login: 'Zaloguj się',
    logout: 'Wyloguj się',
    create_event: 'Utwórz wydarzenie',
    for_organisers: 'Dla Organizatorów',
    lang: {
      sk: '<PERSON><PERSON><PERSON><PERSON>',
      en: '<PERSON><PERSON><PERSON>',
    },
    organiser: {
      title: 'Panel',
      events: 'Wydarzenia:',
      create_event: 'Utwórz wydarzenie',
      index_events: 'Twoje wydarzenia',
      edit_organiser: 'Edytuj organizatora',
      reporting: 'Raportowanie',
      logout: 'Wyloguj się',
    },
  },
  auth: {
    fields: {
      // Step 1
      email: 'Email',
      first_name: '<PERSON><PERSON><PERSON>',
      last_name: 'Nazwi<PERSON>',
      password: '<PERSON>ł<PERSON>',
      password_confirmation: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hasła',

      // Step 2
      organiser_name: '<PERSON><PERSON>wa organizacji',
      contact_email: '<PERSON><PERSON> kont<PERSON>',
      contact_mobile: 'Telefon kontaktowy',
      reg_number: 'Numer rejestracyjny firmy',
      vat_number: 'Numer VAT',
      country: 'Kraj',

      mobile: 'Numer telefonu',

      label_with_optional: ({ linked, named }: I18nTranslation) => `${linked(`auth.fields.${named('fieldName')}`)} (Opcjonalne)`,
    },
    buttons: {
      login: 'Zaloguj się',
      submit: 'Wyślij',
      forgot_password: 'Zapomniałeś hasła?',
      register: 'Zarejestruj się',
      not_registered: 'Nie zarejestrowany?',
      has_account: 'Masz już konto?',
      next: 'Dalej',
      prev: 'Wstecz',
    },
    organiser_login: {
      title: 'Logowanie organizatora',
    },
    login: {
      title: 'Logowanie',
    },
    organiser_registration: {
      title: 'Rejestracja organizatora',
      step_1: 'Podstawowe informacje',
      step_2: 'Informacje o organizacji',
      password_mismatch: 'Hasła nie pasują do siebie',
    },
    pyament: {
      continue: 'Przejdź do płatności',
      processing: 'Przetwarzanie...',
      pay_now: 'Zapłać teraz',
      canceled: 'Płatność anulowana',
      success: 'Płatność zakończona sukcesem!',
      success_confirm_1: 'Twoje bilety na',
      success_confirm_2: 'zostały pomyślnie zarezerwowane.',
      canceled_confirm: 'Twoja płatność została anulowana. Możesz bezpiecznie wrócić na naszą stronę.',
      buttons: {
        home: 'Powrót do strony głównej',
      },
    },
    forgotten_pswd: {
      change: 'Zmień hasło',
      send: 'Wyślij',
      email: 'E-mail',
      sent_email: 'Email zostanie wysłany na Twój adres e-mail, tam możesz zmienić swoje hasło.',
    },
  },
  organiser: {
    reporting: {
      event_report: 'Report podujatia',
      show_all_data: 'Zobraziť všetky údaje',
      start_date: 'Dátum začiatku',
      end_date: 'Dátum konca',
      loading_event_data: 'Načítavanie údajov o podujatí...',
      sales_trend_by_ticket_type: 'Trend predaja podľa typu lístka',
      ticket_types_distribution: 'Distribúcia typov lístkov',
      ticket_type_performance: 'Výkon typov lístkov',
      ticket_type: 'Typ lístka',
      sold: 'Predané',
      revenue: 'Príjem',
      organiser_report: 'Raport Organizatora',
      loading_data: 'Ładowanie danych raportu...',
      total_revenue: 'Całkowite przychody',
      trend: '↑ 12% od zeszłego miesiąca',
      daily_sales: 'Dzienne sprzedaże',
      event_revenue: 'Przychody z wydarzeń',
      sales_trend: 'Trend sprzedaży',
      event_revenue_breakdown: 'Podział przychodów z wydarzeń',
      events_breakdown: 'Podział wydarzeń',
      event: 'Wydarzenie',
    },
    delete_event: {
      text: {
        confirm_del: 'Potwierdź usunięcie',
        sec_confirm_del: 'Czy na pewno chcesz usunąć to wydarzenie? Tej operacji nie można cofnąć.',
      },
      buttons: {
        cancel: 'Anuluj',
        delete: 'Usuń',
        delete_event: 'Usuń wydarzenie',
      },
    },
    create_event: {
      next: 'Dalej',
      prev: 'Wstecz',
    },
    edit_event: {
      data_invalid: 'Wprowadzone zmiany są nieprawidłowe!',
      buttons: {
        edit_event: 'Edytuj wydarzenie',
        basic_setting: 'Podstawowe ustawienia',
        location: 'Lokalizacja',
        additional_sett: 'Dodatkowe ustawienia',
        gallery: 'Galeria',
        tickets: 'Bilety',
        save_changes: 'Zapisz zmiany',
      },
      basic_settings_comp: {
        event_creation: 'Tworzenie wydarzenia',
        basic_settings: 'Podstawowe ustawienia',
      },
      addit_sett_comp: {
        links: 'Linki',
        policies: 'Zasady',
        add_social: 'Dodaj link do mediów społecznościowych',
        add_policy: 'Dodaj zasadę',
        tags: 'Tagi',
      },
      gallery_comp: {
        images_gallery: 'Galeria zdjęć',
      },
      tickets_comp: {
        tickets_attrb: 'Atrybuty biletów',
        discounts: 'Zniżki',
        add_discount: 'Dodaj zniżkę',
        features: 'Funkcje',
        add_feature: 'Dodaj funkcję',
        add_ticket_type: 'Dodaj typ biletu',
        labels: {
          ticket_name: 'Nazwa biletu',
          max_amount: 'Maksymalna ilość',
          available_amount: 'Dostępna ilość',
          price: 'Cena',
          ticket_category: 'Kategoria biletu',
          percentage: 'Procent',
          start_date: 'Data rozpoczęcia',
          end_date: 'Data zakończenia',
          feature_name: 'Nazwa funkcji',
          description: 'Opis',
        },
        yup_texts: {
          name_req: 'Nazwa biletu jest wymagana',
          max_amount_req: 'Maksymalna ilość jest wymagana',
          max_amount_min: 'Maksymalna ilość musi wynosić co najmniej 1',
          available_amnt_req: 'Dostępna ilość jest wymagana',
          available_amnt_min: 'Dostępna ilość musi wynosić co najmniej 1',
          price_req: 'Cena jest wymagana',
          price_min: 'Cena musi wynosić co najmniej 0',
          ticket_type_id_req: 'ID typu biletu jest wymagane',
          ticket_type_id_invld: 'Nieprawidłowy typ biletu',
          percentage_req: 'Procent jest wymagany',
          percentage_min: 'Procent musi wynosić co najmniej 1%',
          percentage_max: 'Procent musi wynosić maksymalnie 100%',
          start_date_req: 'Data rozpoczęcia jest wymagana',
          end_date_req: 'Data zakończenia jest wymagana',
          end_date_min: 'Data zakończenia musi być po dacie rozpoczęcia',
          tckt_discounts_req: 'Zniżki na bilety są wymagane',
          feature_name_req: 'Nazwa funkcji jest wymagana',
          description_req: 'Opis jest wymagany',
          tckt_features_req: 'Funkcje biletów są wymagane',
        },
      },
    },
  },
  public: {
    loading: 'Ładowanie...',
    event_info: {
      see_all_events: 'Zobacz wszystkie wydarzenia',
      open_map: 'Otwórz w mapie',
      date: 'Data',
      gallery: 'Galeria',
      description: 'Opis',
      links: 'Linki do wydarzenia',
      promoter: 'Promotor',
      upcoming: 'Nadchodzące wydarzenia',
      price: {
        variant_available: 'dostępne warianty',
      },
      venue: {
        venue_text: 'Miejsce',
        policies: 'Zasady miejsca',
        details: 'Zobacz szczegóły',
      },
    },
    ticket: {
      tickets: 'Bilety',
      payment_butt: 'Kup bilety',
      get_tickets: 'Zdobądź bilety',
      sold_out: 'Wyprzedane',
      total: 'Razem',
      buttons: {
        continue: 'Kontynuuj',
      },
    },
    index: {
      dropdown: {
        title: 'Szukaj wydarzeń w:',
      },
      filters: {
        filter_X: 'Filtr',
        filterNames: {
          date: 'Data',
          sort: 'Sortuj',
          categories: 'Kategorie',
          price: 'Cena',
        },
        filterTexts: {
          sort: 'sortuj',
          categories: 'kategorie',
          date: 'data',
          price: 'cena',
        },
        holders: {
          min_price: 'Min. €',
          max_price: 'Max. €',
          categories: 'Wybierz kategorię',
        },
        warnings: {
          noValues: 'Najpierw wybierz wartości filtrów!',
          minBiggerThanMax: 'Wartość minimalna nie może być większa niż maksymalna!',
        },
        buttonTexts: {
          reset: 'Resetuj',
          apply: 'Zastosuj',
        },
      },
      sorter: {
        sort: 'Sortuj',
        sort_by: 'Sortuj według:',
        priceOption: 'Cena',
        dateOption: 'Data',
      },
      buttons: {
        seeAll: 'Zobacz wszystkie',
      },
    },
  },
  errors: {
    parse_error: 'Wystąpił błąd, ale aplikacja nie mogła przeanalizować treści odpowiedzi o błędzie: {error}',
  },
}
