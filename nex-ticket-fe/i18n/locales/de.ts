import type { I18nTranslation } from '@/utils/constants'

export default {
  nav: {
    events: 'Veranstaltungen',
    login: 'Anmelden',
    logout: 'Abmelden',
    create_event: 'Veranstaltung erstellen',
    for_organisers: '<PERSON><PERSON><PERSON>',
    lang: {
      sk: 'Slowakisch',
      en: 'Englisch',
    },
    organiser: {
      title: 'Dashboard',
      events: 'Veranstaltungen:',
      create_event: 'Veranstaltung erstellen',
      index_events: 'Ihre Veranstaltungen',
      edit_organiser: 'Veranstalter bearbeiten',
      reporting: 'Berichterstattung',
      logout: 'Abmelden',
    },
  },
  auth: {
    fields: {
      // Step 1
      email: 'E-Mail',
      first_name: '<PERSON><PERSON><PERSON>',
      last_name: 'Nachname',
      password: 'Passwort',
      password_confirmation: 'Passwortbestätigung',

      // Step 2
      organiser_name: 'Organisationsname',
      contact_email: 'Kontakt-E-Mail',
      contact_mobile: 'Kontakt-Handy',
      reg_number: 'Firmenregisternummer',
      vat_number: 'Umsatzsteuer-Identifikationsnummer',
      country: 'Land',

      mobile: 'Handynummer',

      label_with_optional: ({ linked, named }: I18nTranslation) => `${linked(`auth.fields.${named('fieldName')}`)} (Optional)`,
    },
    buttons: {
      login: 'Anmelden',
      submit: 'Einreichen',
      forgot_password: 'Passwort vergessen?',
      register: 'Registrieren',
      not_registered: 'Nicht registriert?',
      has_account: 'Bereits ein Konto?',
      next: 'Weiter',
      prev: 'Zurück',
    },
    organiser_login: {
      title: 'Veranstalter-Login',
    },
    login: {
      title: 'Anmelden',
    },
    organiser_registration: {
      title: 'Veranstalter-Registrierung',
      step_1: 'Grundinformationen',
      step_2: 'Organisationsinformationen',
      password_mismatch: 'Passwörter stimmen nicht überein',
    },
    payment: {
      continue: 'Weiter zur Zahlung',
      processing: 'Verarbeitung...',
      pay_now: 'Jetzt bezahlen',
      canceled: 'Zahlung abgebrochen',
      success: 'Zahlung erfolgreich!',
      success_confirm_1: 'Ihre Tickets für',
      success_confirm_2: 'wurden erfolgreich gebucht.',
      canceled_confirm: 'Ihre Zahlung wurde abgebrochen. Sie können sicher zu unserer Website zurückkehren.',
      buttons: {
        home: 'Zurück zur Startseite',
      },
    },
    forgotten_pswd: {
      change: 'Passwort ändern',
      send: 'Senden',
      email: 'E-Mail',
      sent_email: 'Eine E-Mail wird an Ihre E-Mail-Adresse gesendet, dort können Sie Ihr Passwort ändern.',
    },
  },
  organiser: {
    reporting: {
      event_report: 'Report podujatia',
      show_all_data: 'Zobraziť všetky údaje',
      start_date: 'Dátum začiatku',
      end_date: 'Dátum konca',
      loading_event_data: 'Načítavanie údajov o podujatí...',
      sales_trend_by_ticket_type: 'Trend predaja podľa typu lístka',
      ticket_types_distribution: 'Distribúcia typov lístkov',
      ticket_type_performance: 'Výkon typov lístkov',
      ticket_type: 'Typ lístka',
      sold: 'Predané',
      revenue: 'Príjem',
      organiser_report: 'Veranstalterbericht',
      loading_data: 'Berichtsdaten werden geladen...',
      total_revenue: 'Gesamteinnahmen',
      trend: '↑ 12% vom letzten Monat',
      daily_sales: 'Tägliche Verkäufe',
      event_revenue: 'Veranstaltungseinnahmen',
      sales_trend: 'Verkaufstrend',
      event_revenue_breakdown: 'Aufschlüsselung der Veranstaltungseinnahmen',
      events_breakdown: 'Veranstaltungsaufschlüsselung',
      event: 'Veranstaltung',
    },
    delete_event: {
      text: {
        confirm_del: 'Löschung bestätigen',
        sec_confirm_del: 'Sind Sie sicher, dass Sie diese Veranstaltung löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
      },
      buttons: {
        cancel: 'Abbrechen',
        delete: 'Löschen',
        delete_event: 'Veranstaltung löschen',
      },
    },
    create_event: {
      next: 'Weiter',
      prev: 'Zurück',
    },
    edit_event: {
      data_invalid: 'Die von Ihnen vorgenommenen Änderungen sind nicht gültig!',
      buttons: {
        edit_event: 'Veranstaltung bearbeiten',
        basic_setting: 'Grundeinstellung',
        location: 'Ort',
        additional_sett: 'Zusätzliche Einstellungen',
        gallery: 'Galerie',
        tickets: 'Tickets',
        save_changes: 'Änderungen speichern',
      },
      basic_settings_comp: {
        event_creation: 'Veranstaltungserstellung',
        basic_settings: 'Grundeinstellungen',
      },
      addit_sett_comp: {
        links: 'Links',
        policies: 'Richtlinien',
        add_social: 'Sozialen Medien Link hinzufügen',
        add_policy: 'Richtlinie hinzufügen',
        tags: 'Tags',
      },
      gallery_comp: {
        images_gallery: 'Bildergalerie',
      },
      tickets_comp: {
        tickets_attrb: 'Ticket-Attribute',
        discounts: 'Rabatte',
        add_discount: 'Rabatt hinzufügen',
        features: 'Merkmale',
        add_feature: 'Merkmal hinzufügen',
        add_ticket_type: 'Ticket-Typ hinzufügen',
        labels: {
          ticket_name: 'Ticketname',
          max_amount: 'Maximale Menge',
          available_amount: 'Verfügbare Menge',
          price: 'Preis',
          ticket_category: 'Ticketkategorie',
          percentage: 'Prozentsatz',
          start_date: 'Startdatum',
          end_date: 'Enddatum',
          feature_name: 'Merkmalname',
          description: 'Beschreibung',
        },
        yup_texts: {
          name_req: 'Name des Tickets ist erforderlich',
          max_amount_req: 'Maximale Menge ist erforderlich',
          max_amount_min: 'Maximale Menge muss mindestens 1 betragen',
          available_amnt_req: 'Verfügbare Menge ist erforderlich',
          available_amnt_min: 'Verfügbare Menge muss mindestens 1 betragen',
          price_req: 'Preis ist erforderlich',
          price_min: 'Preis muss mindestens 0 betragen',
          ticket_type_id_req: 'Ticket-Typ-ID ist erforderlich',
          ticket_type_id_invld: 'Ungültiger Ticket-Typ',
          percentage_req: 'Prozentsatz ist erforderlich',
          percentage_min: 'Prozentsatz muss mindestens 1% betragen',
          percentage_max: 'Prozentsatz darf höchstens 100% betragen',
          start_date_req: 'Startdatum ist erforderlich',
          end_date_req: 'Enddatum ist erforderlich',
          end_date_min: 'Enddatum muss nach dem Startdatum liegen',
          tckt_discounts_req: 'Ticket-Rabatte sind erforderlich',
          feature_name_req: 'Merkmalname ist erforderlich',
          description_req: 'Beschreibung ist erforderlich',
          tckt_features_req: 'Ticket-Merkmale sind erforderlich',
        },
      },
    },
  },
  public: {
    loading: 'Laden...',
    event_info: {
      see_all_events: 'Alle Veranstaltungen anzeigen',
      open_map: 'In Karte öffnen',
      date: 'Datum',
      gallery: 'Galerie',
      description: 'Beschreibung',
      links: 'Veranstaltungslinks',
      promoter: 'Veranstalter',
      upcoming: 'Bevorstehende Veranstaltungen',
      price: {
        variant_available: 'Varianten verfügbar',
      },
      venue: {
        venue_text: 'Veranstaltungsort',
        policies: 'Veranstaltungsrichtlinien',
        details: 'Details anzeigen',
      },
    },
    ticket: {
      tickets: 'Tickets',
      payment_butt: 'Tickets kaufen',
      get_tickets: 'Tickets erhalten',
      sold_out: 'Ausverkauft',
      total: 'Gesamt',
      buttons: {
        continue: 'Weiter',
      },
    },
    index: {
      dropdown: {
        title: 'Veranstaltungen suchen in:',
      },
      filters: {
        filter_X: 'Filter',
        filterNames: {
          date: 'Datum',
          sort: 'Sortieren',
          categories: 'Kategorien',
          price: 'Preis',
        },
        filterTexts: {
          sort: 'sortieren',
          categories: 'kategorien',
          date: 'datum',
          price: 'preis',
        },
        holders: {
          min_price: 'Min. €',
          max_price: 'Max. €',
          categories: 'Kategorie wählen',
        },
        warnings: {
          noValues: 'Wählen Sie zuerst die Filterwerte!',
          minBiggerThanMax: 'Min. Wert darf nicht größer als Max. Wert sein!',
        },
        buttonTexts: {
          reset: 'Zurücksetzen',
          apply: 'Anwenden',
        },
      },
      sorter: {
        sort: 'Sortieren',
        sort_by: 'Sortieren nach:',
        priceOption: 'Preis',
        dateOption: 'Datum',
      },
      buttons: {
        seeAll: 'Alle anzeigen',
      },
    },
  },
  errors: {
    parse_error: 'Ein Fehler ist aufgetreten, aber die App konnte den Fehlerantwortkörper nicht parsen: {error}',
  },
}
