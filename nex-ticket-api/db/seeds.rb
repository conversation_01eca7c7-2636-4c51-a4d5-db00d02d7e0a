# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

begin
  puts 'Seeding the database...'
  user = FactoryBot.create(:user, :organiser_account, email: '<EMAIL>', password: 'password')
  tags = []

  10.times do
    tags << FactoryBot.create(:tag)
  end

  # 20.times do
  #   event = FactoryBot.create(:event, organiser_id: user.organiser.id, tags: tags.sample(rand(1..10)))

  #   rand(1..5).times do
  #     FactoryBot.create(:ticket_type, event_id: event.id)
  #   end
  # end

  puts 'Database seeded successfully!'
rescue StandardError => e
  puts e.inspect
  puts e.backtrace
end
