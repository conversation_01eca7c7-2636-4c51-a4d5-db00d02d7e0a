FactoryBot.define do
  factory :event do
    name { Faker::JapaneseMedia::OnePiece.quote }
    description { Faker::Lorem.paragraph }
    start_time { Time.zone.now + 1.day }
    end_time { Time.zone.now + 2.day }
    organiser_id { create(:organiser).id }
    venue_name { [ Faker::JapaneseMedia::SwordArtOnline.location, Faker::JapaneseMedia::OnePiece.location ].sample }
    latitude { Faker::Address.latitude }
    longitude { Faker::Address.longitude }
    social_media_links { [ { platform: :instagram, link: "https://instagram.com/musicmania" }, { platform: :facebook, link: "https://facebook.com/musicmania" } ] }
    policies { [ { type: Faker::Lorem.sentence, details: Faker::JapaneseMedia::OnePiece.akuma_no_mi } ] }
    address_info { }
    currency { [ "EUR" ].sample }
  end
end
