FactoryBot.define do
  factory :ticket_type do
    max_amount { Faker::Number.number(digits: 3) }
    price { Faker::Commerce.price(range: 10..100.0) }
    discounts { [ { percentage: rand(1..100), start_date: Time.now - rand(0..9).days, end_date: Time.now + rand(1..8).days } ] }
    description { Faker::Lorem.paragraph }
    name { Faker::JapaneseMedia::SwordArtOnline.unique.item }
    event_id { create(:event).id }
  end
end
