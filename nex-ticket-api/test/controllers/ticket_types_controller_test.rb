require "test_helper"

class TicketTypesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @ticket_type = ticket_types(:one)
  end

  test "should get index" do
    get ticket_types_url, as: :json
    assert_response :success
  end

  test "should create ticket_type" do
    assert_difference("TicketType.count") do
      post ticket_types_url, params: { ticket_type: { price: 100, name: "VIP", features: [], available_amount: 50, max_amount: 50 } }, as: :json
    end

    assert_response :created
  end

  test "should show ticket_type" do
    get ticket_type_url(@ticket_type), as: :json
    assert_response :success
  end

  test "should update ticket_type" do
    patch ticket_type_url(@ticket_type), params: { ticket_type: { price: 100, name: "VIP", features: [], available_amount: 50, max_amount: 50 } }, as: :json
    assert_response :success
  end

  test "should destroy ticket_type" do
    assert_difference("TicketType.count", -1) do
      delete ticket_type_url(@ticket_type), as: :json
    end
  end
end
