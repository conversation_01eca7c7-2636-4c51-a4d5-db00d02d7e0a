namespace :tags do
  desc "Import event tags"
  task import: :environment do
    upload_tags()
  end
end

def upload_tags
  tag_categories_data = [ {  name: "Music Genre",
                            tags: [ { name: "Alternative" },
                                    { name: "Alternative Rock" },
                                    { name: "Blues" },
                                    { name: "Breakcore" },
                                    { name: "Classical" },
                                    { name: "Country" },
                                    { name: "Dancehall" },
                                    { name: "Disco" },
                                    { name: "D&B" },
                                    { name: "Dubstep" },
                                    { name: "EDM" },
                                    { name: "<PERSON><PERSON><PERSON>" },
                                    { name: "Electroswing" },
                                    { name: "Folk" },
                                    { name: "Folk Rock" },
                                    { name: "Funk" },
                                    { name: "Hardstyle" },
                                    { name: "Hard Rock" },
                                    { name: "Hip Hop" },
                                    { name: "Rap" },
                                    { name: "House" },
                                    { name: "Hyper pop" },
                                    { name: "Christian" },
                                    { name: "IDM" },
                                    { name: "Indie" },
                                    { name: "Indie Pop" },
                                    { name: "Jazz" },
                                    { name: "K-Pop" },
                                    { name: "Latin" },
                                    { name: "Metal" },
                                    { name: "<PERSON>onk" },
                                    { name: "Pop" },
                                    { name: "Pop Punk" },
                                    { name: "Psychedelic Rock" },
                                    { name: "Punk" },
                                    { name: "R&B" },
                                    { name: "Regga<PERSON>" },
                                    { name: "Rock" },
                                    { name: "Soul" },
                                    { name: "Swing" },
                                    { name: "Techno" },
                                    { name: "Trance" },
                                    { name: "Trap" },
                                    { name: "Oldies" },
                                    { name: "70s" },
                                    { name: "80s" },
                                    { name: "90s" },
                                    { name: "Instrumental" } ] } ]
  tags_data = [
    { name: "Acid" }, { name: "Alternative" }, { name: "Alternative Rock" },
    { name: "Blues" }, { name: "Breakcore" }, { name: "Classical" },
    { name: "Comedy" }, { name: "Country" }, { name: "Dancehall" },
    { name: "Disco" }, { name: "D&B" }, { name: "Dubstep" },
    { name: "EDM" }, { name: "Electro" }, { name: "Electroswing" },
    { name: "Emo" }, { name: "Folk" }, { name: "Folk Rock" },
    { name: "Funk" }, { name: "Grime" }, { name: "Grunge" },
    { name: "Goth" }, { name: "Hardstyle" }, { name: "Hard Rock" },
    { name: "Hip Hop" }, { name: "Rap" }, { name: "House" }, { name: "Hyper pop" },
    { name: "Christian" }, { name: "Gospel" }, { name: "IDM" }, { name: "Indie" },
    { name: "Indie Pop" }, { name: "Industrial" }, { name: "Jazz" },
    { name: "K-Pop" }, { name: "Latin" }, { name: "Metal" },
    { name: "Phonk" }, { name: "Pop" }, { name: "Pop Punk" },
    { name: "Psychedelic Rock" }, { name: "Punk" }, { name: "R&B" },
    { name: "Reggae" }, { name: "Rock" }, { name: "Ska" },
    { name: "Soul" }, { name: "Swing" }, { name: "Techno" },
    { name: "Trance" }, { name: "Trap" }, { name: "Oldies" },
    { name: "70s" }, { name: "80s" }, { name: "90s" },
    { name: "Instrumental" }, { name: "Traffic light" }, { name: "DJ" },
    { name: "Live Music" }, { name: "Silent Disco" }, { name: "Open Air" },
    { name: "Club" }, { name: "College" }, { name: "Karaoke" },
    { name: "Dress Code" }, { name: "Welcome drink" }, { name: "Raffle" },
    { name: "Costumes" }, { name: "Themed Costumes" }, { name: "Holiday Themed" },
    { name: "Themed" }
  ]




  tags_data.each do |tag_data|
    Tag.find_or_create_by(name: tag_data[:name])
  end

  tag_categories_data.each do |tag_category_data|
    tag_category = TagCategory.find_or_create_by(name: tag_category_data[:name])
    tag_category_data[:tags].each do |tag_data|
      tag = Tag.find_or_create_by(name: tag_data[:name])
      tag_category.tags << tag
    end
  end

  puts "Tags table has been seeded with hardcoded data."
end
