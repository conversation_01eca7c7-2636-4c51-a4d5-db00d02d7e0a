# lib/tasks/cities_import.rake

namespace :cities do
  desc "Import city/town data from NDJSON files in InitialData/city/<country>"
  task import: :environment do
    upload_states()

    # Path to the city folder
    city_root_path = Rails.root.join("InitialData", "city")

    # Recursively find all *.ndjson files in subfolders (cz, de, gb, pl, sk, etc.)
    ndjson_files = Dir.glob(city_root_path.join("*/**.ndjson"))

    ndjson_files.each do |filepath|
      puts "Processing file: #{filepath}"
      import_ndjson_file(filepath)
    end

    puts "All NDJSON city/town files have been processed!"
  end
end

# Helper method to parse each file line by line
def import_ndjson_file(filepath)
  country_code = File.basename(File.dirname(filepath)).upcase
  state = State.find_by(iso_alpha_2: country_code)

  city_relevance = {
    "Bratislava" => 100,
    "Warsaw" => 500,
    "Berlin" => 400
  }

  File.foreach(filepath) do |line|
    line.strip!
    next if line.empty?

    city_data = JSON.parse(line)

    english_name = city_data.dig("other_names", "name:en")

    next unless city_data["osm_type"] == "node"
    next if english_name.nil? || english_name.empty?

    # 1) Find or initialize by a unique identifier (if you have one)
    city = City.find_or_initialize_by(osm_id: city_data["osm_id"])

    # 2) Assign or update attributes
    city.name         = city_data["name"]
    city.other_names  = city_data["other_names"]
    city.display_name = city_data["display_name"]
    city.address      = city_data["address"]
    city.population   = city_data["population"]
    city.osm_type     = city_data["osm_type"]
    # 'type' is reserved by Rails, so we store it in 'city_type'
    city.city_type    = city_data["type"]
    city.location     = city_data["location"]
    city.bbox         = city_data["bbox"]
    city.state        = state

    city.basic_relevance = city_relevance[english_name] || 0

    city.save!
  end
end

def upload_states
  states_data = [
    { name: "Albania", vat_percentage: 20.0, iso_alpha_2: "AL", iso_alpha_3: "ALB", iso_numeric: "008", calling_code: "+355", currency_code: "ALL", currency_name: "Lek", other_names: { "name:en" => "Albania", "name:sq" => "Shqipëria", "name:pl" => "Albania", "name:sk" => "Albánsko", "name:cs" => "Albánie", "name:de" => "Albanien" } },
    { name: "Andorra", vat_percentage: 20.0, iso_alpha_2: "AD", iso_alpha_3: "AND", iso_numeric: "020", calling_code: "+376", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Andorra", "name:ca" => "Andorra", "name:pl" => "Andora", "name:sk" => "Andorra", "name:cs" => "Andorra", "name:de" => "Andorra" } },
    { name: "Armenia", vat_percentage: 20.0, iso_alpha_2: "AM", iso_alpha_3: "ARM", iso_numeric: "051", calling_code: "+374", currency_code: "AMD", currency_name: "Dram", other_names: { "name:en" => "Armenia", "name:hy" => "Հայաստան", "name:pl" => "Armenia", "name:sk" => "Arménsko", "name:cs" => "Arménie", "name:de" => "Armenien" } },
    { name: "Austria", vat_percentage: 20.0, iso_alpha_2: "AT", iso_alpha_3: "AUT", iso_numeric: "040", calling_code: "+43", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Austria", "name:de" => "Österreich", "name:pl" => "Austria", "name:sk" => "Rakúsko", "name:cs" => "Rakousko" } },
    { name: "Azerbaijan", vat_percentage: 20.0, iso_alpha_2: "AZ", iso_alpha_3: "AZE", iso_numeric: "031", calling_code: "+994", currency_code: "AZN", currency_name: "Manat", other_names: { "name:en" => "Azerbaijan", "name:az" => "Azərbaycan", "name:pl" => "Azerbejdżan", "name:sk" => "Azerbajdžan", "name:cs" => "Ázerbájdžán", "name:de" => "Aserbaidschan" } },
    { name: "Belarus", vat_percentage: 20.0, iso_alpha_2: "BY", iso_alpha_3: "BLR", iso_numeric: "112", calling_code: "+375", currency_code: "BYN", currency_name: "Belarusian ruble", other_names: { "name:en" => "Belarus", "name:be" => "Беларусь", "name:pl" => "Białoruś", "name:sk" => "Bielorusko", "name:cs" => "Bělorusko", "name:de" => "Weißrussland" } },
    { name: "Belgium", vat_percentage: 21.0, iso_alpha_2: "BE", iso_alpha_3: "BEL", iso_numeric: "056", calling_code: "+32", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Belgium", "name:nl" => "België", "name:fr" => "Belgique", "name:de" => "Belgien", "name:pl" => "Belgia", "name:sk" => "Belgicko", "name:cs" => "Belgie" } },
    { name: "Bosnia and Herzegovina", vat_percentage: 20.0, iso_alpha_2: "BA", iso_alpha_3: "BIH", iso_numeric: "070", calling_code: "+387", currency_code: "BAM", currency_name: "Convertible mark", other_names: { "name:en" => "Bosnia and Herzegovina", "name:bs" => "Bosna i Hercegovina", "name:pl" => "Bośnia i Hercegowina", "name:sk" => "Bosna a Hercegovina", "name:cs" => "Bosna a Hercegovina", "name:de" => "Bosnien und Herzegowina" } },
    { name: "Bulgaria", vat_percentage: 20.0, iso_alpha_2: "BG", iso_alpha_3: "BGR", iso_numeric: "100", calling_code: "+359", currency_code: "BGN", currency_name: "Lev", other_names: { "name:en" => "Bulgaria", "name:bg" => "България", "name:pl" => "Bułgaria", "name:sk" => "Bulharsko", "name:cs" => "Bulharsko", "name:de" => "Bulgarien" } },
    { name: "Croatia", vat_percentage: 25.0, iso_alpha_2: "HR", iso_alpha_3: "HRV", iso_numeric: "191", calling_code: "+385", currency_code: "HRK", currency_name: "Kuna", other_names: { "name:en" => "Croatia", "name:hr" => "Hrvatska", "name:pl" => "Chorwacja", "name:sk" => "Chorvátsko", "name:cs" => "Chorvatsko", "name:de" => "Kroatien" } },
    { name: "Cyprus", vat_percentage: 19.0, iso_alpha_2: "CY", iso_alpha_3: "CYP", iso_numeric: "196", calling_code: "+357", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Cyprus", "name:el" => "Κύπρος", "name:tr" => "Kıbrıs", "name:pl" => "Cypr", "name:sk" => "Cyprus", "name:cs" => "Kypr", "name:de" => "Zypern" } },
    { name: "Czech Republic", vat_percentage: 21.0, iso_alpha_2: "CZ", iso_alpha_3: "CZE", iso_numeric: "203", calling_code: "+420", currency_code: "CZK", currency_name: "Czech koruna", other_names: { "name:en" => "Czech Republic", "name:cs" => "Česká republika", "name:pl" => "Czechy", "name:sk" => "Česká republika", "name:de" => "Tschechische Republik" } },
    { name: "Denmark", vat_percentage: 25.0, iso_alpha_2: "DK", iso_alpha_3: "DNK", iso_numeric: "208", calling_code: "+45", currency_code: "DKK", currency_name: "Krone", other_names: { "name:en" => "Denmark", "name:da" => "Danmark", "name:pl" => "Dania", "name:sk" => "Dánsko", "name:cs" => "Dánsko", "name:de" => "Dänemark" } },
    { name: "Estonia", vat_percentage: 22.0, iso_alpha_2: "EE", iso_alpha_3: "EST", iso_numeric: "233", calling_code: "+372", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Estonia", "name:et" => "Eesti", "name:pl" => "Estonia", "name:sk" => "Estónsko", "name:cs" => "Estonsko", "name:de" => "Estland" } },
    { name: "Finland", vat_percentage: 25.5, iso_alpha_2: "FI", iso_alpha_3: "FIN", iso_numeric: "246", calling_code: "+358", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Finland", "name:fi" => "Suomi", "name:sv" => "Finland", "name:pl" => "Finlandia", "name:sk" => "Fínsko", "name:cs" => "Finsko", "name:de" => "Finnland" } },
    { name: "France", vat_percentage: 20.0, iso_alpha_2: "FR", iso_alpha_3: "FRA", iso_numeric: "250", calling_code: "+33", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "France", "name:fr" => "France", "name:pl" => "Francja", "name:sk" => "Francúzsko", "name:cs" => "Francie", "name:de" => "Frankreich" } },
    { name: "Georgia", vat_percentage: 20.0, iso_alpha_2: "GE", iso_alpha_3: "GEO", iso_numeric: "268", calling_code: "+995", currency_code: "GEL", currency_name: "Lari", other_names: { "name:en" => "Georgia", "name:ka" => "საქართველო", "name:pl" => "Gruzja", "name:sk" => "Gruzínsko", "name:cs" => "Gruzie", "name:de" => "Georgien" } },
    { name: "Germany", vat_percentage: 19.0, iso_alpha_2: "DE", iso_alpha_3: "DEU", iso_numeric: "276", calling_code: "+49", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Germany", "name:de" => "Deutschland", "name:pl" => "Niemcy", "name:sk" => "Nemecko", "name:cs" => "Německo" } },
    { name: "Greece", vat_percentage: 24.0, iso_alpha_2: "GR", iso_alpha_3: "GRC", iso_numeric: "300", calling_code: "+30", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Greece", "name:el" => "Ελλάδα", "name:pl" => "Grecja", "name:sk" => "Grécko", "name:cs" => "Řecko", "name:de" => "Griechenland" } },
    { name: "Hungary", vat_percentage: 27.0, iso_alpha_2: "HU", iso_alpha_3: "HUN", iso_numeric: "348", calling_code: "+36", currency_code: "HUF", currency_name: "Forint", other_names: { "name:en" => "Hungary", "name:hu" => "Magyarország", "name:pl" => "Węgry", "name:sk" => "Maďarsko", "name:cs" => "Maďarsko", "name:de" => "Ungarn" } },
    { name: "Iceland", vat_percentage: 20.0, iso_alpha_2: "IS", iso_alpha_3: "ISL", iso_numeric: "352", calling_code: "+354", currency_code: "ISK", currency_name: "Icelandic króna", other_names: { "name:en" => "Iceland", "name:is" => "Ísland", "name:pl" => "Islandia", "name:sk" => "Island", "name:cs" => "Island", "name:de" => "Island" } },
    { name: "Ireland", vat_percentage: 23.0, iso_alpha_2: "IE", iso_alpha_3: "IRL", iso_numeric: "372", calling_code: "+353", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Ireland", "name:ga" => "Éire", "name:pl" => "Irlandia", "name:sk" => "Írsko", "name:cs" => "Irsko", "name:de" => "Irland" } },
    { name: "Italy", vat_percentage: 22.0, iso_alpha_2: "IT", iso_alpha_3: "ITA", iso_numeric: "380", calling_code: "+39", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Italy", "name:it" => "Italia", "name:pl" => "Włochy", "name:sk" => "Taliansko", "name:cs" => "Itálie", "name:de" => "Italien" } },
    { name: "Kazakhstan", vat_percentage: 20.0, iso_alpha_2: "KZ", iso_alpha_3: "KAZ", iso_numeric: "398", calling_code: "+7", currency_code: "KZT", currency_name: "Tenge", other_names: { "name:en" => "Kazakhstan", "name:kk" => "Қазақстан", "name:pl" => "Kazachstan", "name:sk" => "Kazachstan", "name:cs" => "Kazachstán", "name:de" => "Kasachstan" } },
    { name: "Kosovo", vat_percentage: 20.0, iso_alpha_2: "XK", iso_alpha_3: "XKX", iso_numeric: "383", calling_code: "+383", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Kosovo", "name:sq" => "Kosova", "name:sr" => "Косово", "name:pl" => "Kosowo", "name:sk" => "Kosovo", "name:cs" => "Kosovo", "name:de" => "Kosovo" } },
    { name: "Latvia", vat_percentage: 21.0, iso_alpha_2: "LV", iso_alpha_3: "LVA", iso_numeric: "428", calling_code: "+371", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Latvia", "name:lv" => "Latvija", "name:pl" => "Łotwa", "name:sk" => "Lotyšsko", "name:cs" => "Lotyšsko", "name:de" => "Lettland" } },
    { name: "Liechtenstein", vat_percentage: 20.0, iso_alpha_2: "LI", iso_alpha_3: "LIE", iso_numeric: "438", calling_code: "+423", currency_code: "CHF", currency_name: "Swiss franc", other_names: { "name:en" => "Liechtenstein", "name:de" => "Liechtenstein", "name:pl" => "Liechtenstein", "name:sk" => "Lichtenštajnsko", "name:cs" => "Lichtenštejnsko" } },
    { name: "Lithuania", vat_percentage: 21.0, iso_alpha_2: "LT", iso_alpha_3: "LTU", iso_numeric: "440", calling_code: "+370", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Lithuania", "name:lt" => "Lietuva", "name:pl" => "Litwa", "name:sk" => "Litva", "name:cs" => "Litva", "name:de" => "Litauen" } },
    { name: "Luxembourg", vat_percentage: 21.0, iso_alpha_2: "LU", iso_alpha_3: "LUX", iso_numeric: "442", calling_code: "+352", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Luxembourg", "name:lb" => "Lëtzebuerg", "name:fr" => "Luxembourg", "name:de" => "Luxemburg", "name:pl" => "Luksemburg", "name:sk" => "Luxembursko", "name:cs" => "Lucembursko" } },
    { name: "Malta", vat_percentage: 18.0, iso_alpha_2: "MT", iso_alpha_3: "MLT", iso_numeric: "470", calling_code: "+356", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Malta", "name:mt" => "Malta", "name:pl" => "Malta", "name:sk" => "Malta", "name:cs" => "Malta", "name:de" => "Malta" } },
    { name: "Moldova", vat_percentage: 20.0, iso_alpha_2: "MD", iso_alpha_3: "MDA", iso_numeric: "498", calling_code: "+373", currency_code: "MDL", currency_name: "Moldovan leu", other_names: { "name:en" => "Moldova", "name:ro" => "Moldova", "name:pl" => "Mołdawia", "name:sk" => "Moldavsko", "name:cs" => "Moldavsko", "name:de" => "Moldawien" } },
    { name: "Monaco", vat_percentage: 20.0, iso_alpha_2: "MC", iso_alpha_3: "MCO", iso_numeric: "492", calling_code: "+377", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Monaco", "name:fr" => "Monaco", "name:pl" => "Monako", "name:sk" => "Monako", "name:cs" => "Monako", "name:de" => "Monaco" } },
    { name: "Montenegro", vat_percentage: 20.0, iso_alpha_2: "ME", iso_alpha_3: "MNE", iso_numeric: "499", calling_code: "+382", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Montenegro", "name:sr" => "Црна Гора", "name:hr" => "Crna Gora", "name:pl" => "Czarnogóra", "name:sk" => "Čierna Hora", "name:cs" => "Černá Hora", "name:de" => "Montenegro" } },
    { name: "Netherlands", vat_percentage: 21.0, iso_alpha_2: "NL", iso_alpha_3: "NLD", iso_numeric: "528", calling_code: "+31", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Netherlands", "name:nl" => "Nederland", "name:pl" => "Holandia", "name:sk" => "Holandsko", "name:cs" => "Nizozemsko", "name:de" => "Niederlande" } },
    { name: "North Macedonia", vat_percentage: 20.0, iso_alpha_2: "MK", iso_alpha_3: "MKD", iso_numeric: "807", calling_code: "+389", currency_code: "MKD", currency_name: "Denar", other_names: { "name:en" => "North Macedonia", "name:mk" => "Северна Македонија", "name:pl" => "Macedonia Północna", "name:sk" => "Severné Makedónsko", "name:cs" => "Severní Makedonie", "name:de" => "Nordmazedonien" } },
    { name: "Norway", vat_percentage: 20.0, iso_alpha_2: "NO", iso_alpha_3: "NOR", iso_numeric: "578", calling_code: "+47", currency_code: "NOK", currency_name: "Norwegian krone", other_names: { "name:en" => "Norway", "name:no" => "Norge", "name:pl" => "Norwegia", "name:sk" => "Nórsko", "name:cs" => "Norsko", "name:de" => "Norwegen" } },
    { name: "Poland", vat_percentage: 23.0, iso_alpha_2: "PL", iso_alpha_3: "POL", iso_numeric: "616", calling_code: "+48", currency_code: "PLN", currency_name: "Polish złoty", other_names: { "name:en" => "Poland", "name:pl" => "Polska", "name:sk" => "Poľsko", "name:cs" => "Polsko", "name:de" => "Polen" } },
    { name: "Portugal", vat_percentage: 23.0, iso_alpha_2: "PT", iso_alpha_3: "PRT", iso_numeric: "620", calling_code: "+351", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Portugal", "name:pt" => "Portugal", "name:pl" => "Portugalia", "name:sk" => "Portugalsko", "name:cs" => "Portugalsko", "name:de" => "Portugal" } },
    { name: "Romania", vat_percentage: 19.0, iso_alpha_2: "RO", iso_alpha_3: "ROU", iso_numeric: "642", calling_code: "+40", currency_code: "RON", currency_name: "Romanian leu", other_names: { "name:en" => "Romania", "name:ro" => "România", "name:pl" => "Rumunia", "name:sk" => "Rumunsko", "name:cs" => "Rumunsko", "name:de" => "Rumänien" } },
    { name: "Russia", vat_percentage: 20.0, iso_alpha_2: "RU", iso_alpha_3: "RUS", iso_numeric: "643", calling_code: "+7", currency_code: "RUB", currency_name: "Russian ruble", other_names: { "name:en" => "Russia", "name:ru" => "Россия", "name:pl" => "Rosja", "name:sk" => "Rusko", "name:cs" => "Rusko", "name:de" => "Russland" } },
    { name: "San Marino", vat_percentage: 20.0, iso_alpha_2: "SM", iso_alpha_3: "SMR", iso_numeric: "674", calling_code: "+378", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "San Marino", "name:it" => "San Marino", "name:pl" => "San Marino", "name:sk" => "San Maríno", "name:cs" => "San Marino", "name:de" => "San Marino" } },
    { name: "Serbia", vat_percentage: 20.0, iso_alpha_2: "RS", iso_alpha_3: "SRB", iso_numeric: "688", calling_code: "+381", currency_code: "RSD", currency_name: "Serbian dinar", other_names: { "name:en" => "Serbia", "name:sr" => "Србија", "name:pl" => "Serbia", "name:sk" => "Srbsko", "name:cs" => "Srbsko", "name:de" => "Serbien" } },
    { name: "Slovakia", vat_percentage: 23.0, iso_alpha_2: "SK", iso_alpha_3: "SVK", iso_numeric: "703", calling_code: "+421", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Slovakia", "name:sk" => "Slovensko", "name:pl" => "Słowacja", "name:cs" => "Slovensko", "name:de" => "Slowakei" } },
    { name: "Slovenia", vat_percentage: 22.0, iso_alpha_2: "SI", iso_alpha_3: "SVN", iso_numeric: "705", calling_code: "+386", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Slovenia", "name:sl" => "Slovenija", "name:pl" => "Słowenia", "name:sk" => "Slovinsko", "name:cs" => "Slovinsko", "name:de" => "Slowenien" } },
    { name: "Spain", vat_percentage: 21.0, iso_alpha_2: "ES", iso_alpha_3: "ESP", iso_numeric: "724", calling_code: "+34", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Spain", "name:es" => "España", "name:pl" => "Hiszpania", "name:sk" => "Španielsko", "name:cs" => "Španělsko", "name:de" => "Spanien" } },
    { name: "Sweden", vat_percentage: 25.0, iso_alpha_2: "SE", iso_alpha_3: "SWE", iso_numeric: "752", calling_code: "+46", currency_code: "SEK", currency_name: "Swedish krona", other_names: { "name:en" => "Sweden", "name:sv" => "Sverige", "name:pl" => "Szwecja", "name:sk" => "Švédsko", "name:cs" => "Švédsko", "name:de" => "Schweden" } },
    { name: "Switzerland", vat_percentage: 20.0, iso_alpha_2: "CH", iso_alpha_3: "CHE", iso_numeric: "756", calling_code: "+41", currency_code: "CHF", currency_name: "Swiss franc", other_names: { "name:en" => "Switzerland", "name:de" => "Schweiz", "name:fr" => "Suisse", "name:it" => "Svizzera", "name:rm" => "Svizra", "name:pl" => "Szwajcaria", "name:sk" => "Švajčiarsko", "name:cs" => "Švýcarsko" } },
    { name: "Turkey", vat_percentage: 20.0, iso_alpha_2: "TR", iso_alpha_3: "TUR", iso_numeric: "792", calling_code: "+90", currency_code: "TRY", currency_name: "Turkish lira", other_names: { "name:en" => "Turkey", "name:tr" => "Türkiye", "name:pl" => "Turcja", "name:sk" => "Turecko", "name:cs" => "Turecko", "name:de" => "Türkei" } },
    { name: "Ukraine", vat_percentage: 20.0, iso_alpha_2: "UA", iso_alpha_3: "UKR", iso_numeric: "804", calling_code: "+380", currency_code: "UAH", currency_name: "Hryvnia", other_names: { "name:en" => "Ukraine", "name:uk" => "Україна", "name:pl" => "Ukraina", "name:sk" => "Ukrajina", "name:cs" => "Ukrajina", "name:de" => "Ukraine" } },
    { name: "United Kingdom", vat_percentage: 20.0, iso_alpha_2: "GB", iso_alpha_3: "GBR", iso_numeric: "826", calling_code: "+44", currency_code: "GBP", currency_name: "Pound sterling", other_names: { "name:en" => "United Kingdom", "name:cy" => "Y Deyrnas Unedig", "name:pl" => "Wielka Brytania", "name:sk" => "Spojené kráľovstvo", "name:cs" => "Spojené království", "name:de" => "Vereinigtes Königreich" } },
    { name: "Vatican City", vat_percentage: 20.0, iso_alpha_2: "VA", iso_alpha_3: "VAT", iso_numeric: "336", calling_code: "+379", currency_code: "EUR", currency_name: "Euro", other_names: { "name:en" => "Vatican City", "name:it" => "Città del Vaticano", "name:pl" => "Watykan", "name:sk" => "Vatikán", "name:cs" => "Vatikán", "name:de" => "Vatikanstadt" } }
  ]

  states_data.each do |state_data|
    state = State.find_or_initialize_by(iso_alpha_2: state_data[:iso_alpha_2])
    state.update!(state_data)
  end

  puts "States table has been seeded with hardcoded data."
end
