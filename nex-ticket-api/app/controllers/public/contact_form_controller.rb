module Public
  class ContactFormController < ApplicationController
    def send_message_to_promoter
      promoter_email = contact_params[:promoter_email]
      sender_email = contact_params[:email]
      message = contact_params[:message]
      name = contact_params[:name]
      subject = contact_params[:subject]

      if promoter_email.present? && sender_email.present? && message.present? && name.present? && subject.present?
        PromoterMailer.with(contact_params).send_promoter_mail.deliver_now
        render json: { status: "success", message: "Em<PERSON> sent successfully" }, status: :ok
      else
        render json: { status: "error", message: "Missing required parameters" }, status: :unprocessable_entity
      end
    end

    private

    def contact_params
      params.permit(:promoter_email, :email, :message, :name, :subject)
    end
  end
end
