# app/controllers/payments_controller.rb
require "stripe"
module Public
  class StripePaymentsController < ApplicationController
    include Customerable

    def create_stripe_checkout_session
      order = Order.find_by(order_id: get_params[:order_id])
      line_items = order.order_items.map do |item|
        {
          price_data: {
            currency: order.currency_code,
            product_data: {
              name: item.itemable_name
            },
            unit_amount: (item.price_per_piece * 100).to_i # Convert to cents
          },
          quantity: item.quantity
        }
      end

      session = Stripe::Checkout::Session.create({
        line_items: line_items,
        mode: "payment",
        metadata: { order_id: order.id },
        payment_intent_data: {
          metadata: { order_id: order.id }
        },
        ui_mode: "custom",
        return_url: "#{ENV["PUBLIC_URL"] || 'http://localhost:3000'}/payments/success?session_id={CHECKOUT_SESSION_ID}"
      })

      order.update(external_payment_gate_type: :stripe, external_payment_gate_id: session.id, status: :processing)

      render json: { id: session.client_secret }
    end

    def stripe_webhook
      payload = request.body.read
      sig_header = request.env["HTTP_STRIPE_SIGNATURE"]
      endpoint_secret = ENV["STRIPE_WEBHOOK_SECRET"]

      begin
        event = Stripe::Webhook.construct_event(
          payload, sig_header, endpoint_secret
        )

        case event.type
        when "payment_intent.succeeded"
            handle_successful_payment(event.data.object)
        when "payment_intent.payment_failed"
            handle_failed_payment(event.data.object)
        when "checkout.session.completed"
        # Nothing for now
        else
          render plain: "Unhandled Stripe Event: #{event.type}", status: :bad_request
        end

        head :ok

      rescue Stripe::SignatureVerificationError => e
          render plain: "Signature verification failed: #{e}", status: :bad_request
      rescue JSON::ParserError
          render plain: "Invalid payload", status: :bad_request
      end
    end

    private

    def handle_successful_payment(payment_intent)
      order = Order.find(payment_intent.metadata.order_id)
      order.mark_as_success!(:stripe, payment_intent.id)
    end

    def handle_failed_payment(payment_intent)
      order.update(status: :failed)
    end

    def get_params
      params.permit(*PAYMENT_PARAMS)
    end
  end
end
