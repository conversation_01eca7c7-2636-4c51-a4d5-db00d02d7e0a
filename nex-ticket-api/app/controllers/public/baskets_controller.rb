module Public
  class BasketsController < ApplicationController
    rescue_from InsufficientAvailableAmountError, with: :handle_insufficient_available_amount
    include Customerable
    before_action :set_basket

    def show
      render json: BasketSerializer.new(@basket, include: [ :basket_items ]).serializable_hash
    end

    def remove_item
      basket_item = @basket.basket_items.find_by(id: params[:id])
      if basket_item && basket_item.basket == @basket
        basket_item.destroy
        head :no_content
      else
        render json: { error: "Item not found in basket or does not belong to the current user/visitor" }, status: :not_found
      end
    end

    def update_item
      basket_item = @basket.basket_items.find_or_initialize_by(itemable_type: basket_params[:itemable_type], itemable_id: basket_params[:itemable_id])
      if params[:quantity].to_i <= 0
        basket_item.destroy
        head :no_content
      else
        basket_item.quantity = params[:quantity]
        if basket_item.save
          render json: basket_item
        else
          render_error(basket_item)
        end
      end
    end

    private

    def set_basket
      customer = find_or_create_customer
      @basket = Basket.find_or_create_by(customer: customer)
      @basket.renew_lifetime if @basket.persisted?
    end

    def basket_params
      params.permit(*BASKET_PARAMS)
    end

    def render_error(resource)
      render json: { errors: resource.errors.full_messages }, status: :unprocessable_entity
    end

    def handle_insufficient_available_amount(exception)
      render json: { error: exception.message }, status: :unprocessable_entity
    end
  end
end
