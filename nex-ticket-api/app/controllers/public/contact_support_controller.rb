module Public
  class ContactSupportController < ApplicationController
    def send_message_to_support
      sender_email = contact_params[:email]
      message = contact_params[:message]
      name = contact_params[:name]
      subject = contact_params[:subject]

      if sender_email.present? && message.present? && name.present? && subject.present?
        SupportMailer.with(contact_params).send_support_mail.deliver_now
        render json: { status: "success", message: "Email sent successfully" }, status: :ok
      else
        render json: { status: "error", message: "Missing required parameters" }, status: :unprocessable_entity
      end
    end

    private

    def contact_params
      params.permit(:email, :message, :name, :subject)
    end
  end
end
