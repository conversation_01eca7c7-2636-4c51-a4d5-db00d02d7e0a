# app/controllers/city_search_controller.rb
class CitySearchController < ApplicationController
  before_action :set_city, only: %i[ show ]

  SERIALIZER_INCLUDES = [ "state" ]
  def index
    lang = I18n.locale.to_s

    if params[:q].present?
      query = params[:q]
      contains_pattern = "%#{query}%"
      starts_with_pattern = "#{query}%"

      quoted_starts_with = ActiveRecord::Base.connection.quote(starts_with_pattern)
      quoted_lang = ActiveRecord::Base.connection.quote("name:#{lang}")

      priority_sql = <<-SQL
        CASE
          WHEN name ILIKE #{quoted_starts_with} THEN 1
          WHEN other_names->>#{quoted_lang} ILIKE #{quoted_starts_with} THEN 1
          ELSE 2
        END
      SQL

      @cities = City
        .select("cities.*", "(#{priority_sql}) AS sort_priority")
        .where("other_names->>'name:#{quoted_lang}' ILIKE ? OR name ILIKE ?", contains_pattern, contains_pattern)
        .order("sort_priority ASC, name ASC")

    else
      @cities = City.all.order(basic_relevance: :desc)
    end

    render json: @cities
  end

  # GET /cities/1
  def show
    serializer = CitySerializer.new(@city, include: SERIALIZER_INCLUDES)
    render json: serializer.serializable_hash
  end

private
  def set_city
    @city = City.find(params[:id])
  end
end
