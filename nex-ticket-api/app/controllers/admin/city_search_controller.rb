module Admin
  class CitySearchController < ApplicationController
    # before_action :authorize_admin!
    before_action :set_city, only: [ :show, :update, :destroy ]
    before_action :authenticate_admin!

    # GET /cities
    def index
      lang = I18n.locale.to_s
      if params[:q].present?
        cities = City.where("other_names->>'name:#{lang}' ILIKE ? OR name ILIKE ?", "%#{params[:q]}%", "%#{params[:q]}%").order("basic_relevance DESC")
      else
        cities = City.all.order("basic_relevance DESC")
      end

      render json: cities
    end

    # GET /cities/1
    def show
      render json: @city
    end

    # POST /cities
    def create
      city = City.new(city_params)
      if city.save
        render json: city, status: :created
      else
        render json: city.errors, status: :unprocessable_entity
      end
    end

    # PATCH/PUT /cities/1
    def update
      if @city.update(city_params)
        render json: @city
      else
        render json: @city.errors, status: :unprocessable_entity
      end
    end

    # DELETE /cities/1
    def destroy
      @city.destroy
      head :no_content
    end

    private

    def set_city
      @city = City.find(params[:id])
    end

    def authorize_admin!
      render json: { error: "Unauthorized access" }, status: :unauthorized unless current_user.is_admin?
    end

    def city_params
      params.permit(*CITY_PARAMS)
    end
  end
end
