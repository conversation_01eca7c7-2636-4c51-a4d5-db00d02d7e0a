module Organisers
  class EventsController < ApplicationController
    include Paginationable

    ALLOWED_INCLUDES = %w[ticket_types organiser tags].freeze

    before_action -> { filter_and_set_includes params[:includes] }
    before_action :set_event, only: %i[show update destroy]
    before_action :authenticate_user!
    before_action :authenticate_organiser!

    # GET /events
    def index
      if current_user.is_admin?
        events = Event.includes(@includes).limit(per_page).offset(paginate_offset)
      elsif current_user.is_organiser?
        events = current_user.organiser.events.includes(@includes).limit(per_page).offset(paginate_offset)
      else
        render json: { error: "No associated organiser found" }, status: :unprocessable_entity and return
      end
      render json: EventSerializer.new(events, { include: @includes, params: serializer_params }).serializable_hash
    end

    # GET /events/1
    def show
      if current_user.is_admin? || (current_user.organiser && current_user.organiser.owns_event?(@event))
        render json: EventSerializer.new(@event, { include: @includes, params: serializer_params }).serializable_hash, status: :ok
      else
        render json: { error: "Unauthorized access" }, status: :unprocessable_entity
      end
    end

    # POST /events
    def create
      if current_user.is_organiser?
        @event = current_user.organiser.events.build(event_params_with_includes)
        attach_files(@event)
        update_event_address(@event)
        @event.ticket_types.each do |ticket_type|
          ticket_type.available_amount = ticket_type.max_amount
        end
        if @event.save
          render json: EventSerializer.new(@event, { include: [ :ticket_types, :tags ], params: serializer_params }).serializable_hash
        else
          render json: @event.errors, status: :unprocessable_entity
        end
      else
        render json: { error: "No associated organiser found" }, status: :unprocessable_entity
      end
    end

    # PATCH/PUT /events/1
    def update
      if current_user.is_admin? || (current_user.organiser && current_user.organiser.owns_event?(@event))
        attach_files(@event)
        @event.ticket_types.each do |ticket_type|
          ticket_type.available_amount = ticket_type.max_amount
        end
        if @event.update(event_params_with_includes)
        update_event_address(@event)
          render json: EventSerializer.new(@event, { include: [ :ticket_types, :tags ], params: serializer_params }).serializable_hash
        else
          render json: @event.errors, status: :unprocessable_entity
        end
      else
        render json: { error: "Unauthorized access" }, status: :unprocessable_entity
      end
    end

    # DELETE /events/1
    def destroy
      if current_user.is_admin? || (current_user.organiser && current_user.organiser.owns_event?(@event))
        begin
          @event.destroy!
          head :no_content
        rescue ActiveRecord::InvalidForeignKey => e
          render json: { error: "Cannot delete event with existing orders." }, status: :conflict
        rescue ActiveRecord::RecordNotDestroyed => e
          render json: { error: "Event could not be destroyed: #{e.message}" }, status: :unprocessable_entity
        end
      else
        render json: { error: "Unauthorized access" }, status: :forbidden
      end
    end

    private

    def filter_and_set_includes(includes_param)
      requested_includes = includes_param.to_s.split(",").map(&:strip)
      @includes = requested_includes.select { |association| ALLOWED_INCLUDES.include?(association) }
    end

    def set_event
      @event = Event.includes(@includes).find(params[:id])
    end

    def event_params
      params.require(:event).permit(*EVENT_PARAMS)
    end

    def event_params_with_includes
      params.require(:event).permit(*EVENT_PARAMS_WITH_INCLUDES)
    end

    def update_event_address(event)
      service = GeoapifyReverseGeocoding.new(event.latitude, event.longitude)
      address_info = service.fetch_address

      if address_info.present?
        city_name = address_info.dig("features", 0, "properties", "city")
        cities = City.where("other_names->>'name:en' = ?", city_name)
        if cities.count == 1
          city = cities.first
          event.address_info = address_info
          event.city_id = city.id if event.city_id.nil?
        elsif cities.count > 1
          raise "Multiple cities found with the name: #{city_name}"
        else
          raise "City not found: #{city_name}"
        end
      end
    end

    def attach_files(event)
      if params[:event][:main_photo].present?
        main_blob = ActiveStorage::Blob.find_by(key: params[:event][:main_photo])
        if main_blob&.image? && main_blob.metadata["uploaded_by"]["user_id"] == current_user.id
          event.main_photo.attach(main_blob)
        else
          event.errors.add(:main_photo, "is not valid or you don't have permission")
        end
      end

      if params[:event][:photo_gallery].present?
        ActiveStorage::Blob.where(key: params[:event][:photo_gallery]).each do |blob|
          if blob.image? && blob.metadata["uploaded_by"]["user_id"] == current_user.id
            event.photo_gallery.attach(blob)
          end
        end
      end
    end
  end
end
