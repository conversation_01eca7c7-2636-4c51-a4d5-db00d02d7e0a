
class ApplicationController < ActionController::API
  include ActionController::MimeResponds

  respond_to :json

  before_action :set_language_from_header

  # Basic
  EVENT_PARAMS = [ :name, :description, :start_time, :end_time, :organiser_id, :city_id,  :venue_name, :currency, :longitude, :latitude, tag_ids: [], social_media_links: [ [ :platform, :link ] ], policies: [ [ :type, :details ] ] ].freeze
  TICKET_TYPES_PARAMS = [ :event_id, :max_amount, :discounted_price, :price, :name, :description, discounts: [ [ :percentage, :start_date, :end_date ] ] ].freeze
  TICKET_TYPE_PROMO_PARAMS = [ :ticket_type_id, :promo_code, :type_of_promo, promo_data: {} ].freeze
  ORGANISER_PARAMS = [ :name, :contact_email, :contact_mobile, :default_currency, :reg_number, :vat_number, :state_id ].freeze
  USER_PARAMS = [ :first_name, :last_name, :type, organiser_attributes: ORGANISER_PARAMS ].freeze
  CITY_PARAMS = [ :name, :display_name, :population, :osm_type, :osm_id, :basic_relevance, other_names: {}, address: {}, location: [], bbox: [] ].freeze
  BASKET_PARAMS = [ :quantity, :itemable_type, :itemable_id ].freeze
  PAYMENT_PARAMS = [ :order_id ].freeze

  # With Includes
  EVENT_PARAMS_WITH_INCLUDES = [ *EVENT_PARAMS, ticket_types_attributes: [ :id, TICKET_TYPES_PARAMS ] ]

  respond_to :json

  protected
  def authenticate_admin!
    unless current_user.is_admin?
      render json: { error: "Unauthorized access" }, status: :unauthorized
    end
  end

  def authenticate_organiser!
    unless current_user.is_organiser?
      render json: { error: "Unauthorized access" }, status: :unauthorized
    end
  end

  def set_language_from_header
    locale = extract_locale_from_accept_language_header
    locale = locale.to_sym if locale
    I18n.locale = locale if I18n.available_locales.include?(locale)
  end

  def serializer_params
    {
      admin: current_user&.is_admin?,
      organiser: current_user&.is_organiser?
    }
  end

  rescue_from CanCan::AccessDenied do |exception|
    render json: { error: "Unauthorized access" }, status: :forbidden
  end

  private

  def extract_locale_from_accept_language_header
    request.env["HTTP_ACCEPT_LANGUAGE"]&.scan(/^[a-z]{2}/)&.first
  end

  def render_error(resource, status = :unprocessable_entity)
    render json: { errors: resource.errors.full_messages }, status: status
  end
end
