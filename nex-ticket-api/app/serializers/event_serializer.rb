class EventSerializer
  include JSONAPI::Serializer

  attributes :name, :description, :start_time, :end_time, :venue_name, :currency, :main_photo, :social_media_links, :policies, :photo_gallery, :latitude, :longitude

  attribute :main_photo do |event|
    if event.main_photo.attached?
      {
        url: Rails.application.routes.url_helpers.rails_blob_url(event.main_photo, only_path: true),
        key: event.main_photo.blob.key
      }
    end
  end

  attribute :photo_gallery do |event|
    if event.photo_gallery.attached?
      event.photo_gallery.map do |photo|
        {
          url: Rails.application.routes.url_helpers.rails_blob_url(photo, only_path: true),
          key: photo.blob.key
        }
      end
    end
  end

  has_many :ticket_types, serializer: TicketTypeSerializer do |object|
    object.ticket_types.order(price: :asc)
  end
  has_many :tags, serializer: TagSerializer
  belongs_to :organiser, serializer: OrganiserSerializer

  attribute :city do |object|
    object.address_info.dig("features", 0, "properties", "city") if object.address_info.present?
  end

  attribute :venue_address, key: :venueAddress do |object|
    object.address_info.dig("features", 0, "properties", "formatted") if object.address_info.present?
  end

  attribute :saved do |object, params|
    object.saved_by?(params[:current_user]) || false
  end

  set_key_transform :camel_lower
end
