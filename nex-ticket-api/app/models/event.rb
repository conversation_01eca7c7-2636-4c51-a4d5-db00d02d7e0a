class Event < ApplicationRecord
  default_scope { includes(:saved_events) }

  # Associations
  has_many :ticket_types, dependent: :destroy, inverse_of: :event
  belongs_to :organiser
  accepts_nested_attributes_for :ticket_types
  belongs_to :city, optional: true
  has_many :orders

  has_and_belongs_to_many :tags
  has_one_attached :main_photo
  has_many_attached :photo_gallery
  has_many :saved_events, dependent: :destroy

  # Validations
  validates :name, presence: true, length: { maximum: 255 } # Ensure name is present and not too long
  validates :description, presence: true                   # Ensure description is present
  validates :start_time, presence: true                    # Ensure start_time is present
  validates :end_time, presence: true                      # Ensure end_time is present
  validate :end_time_after_start_time                      # Custom validation for time range
  validates :venue_name, presence: true, length: { maximum: 255 } # Ensure venue name is present and not too long
  validates :currency, length: { maximum: 3 }

  validates :latitude, presence: true, numericality: { greater_than_or_equal_to: -90, less_than_or_equal_to: 90 }
  validates :longitude, presence: true, numericality: { greater_than_or_equal_to: -180, less_than_or_equal_to: 180 }
  validate :social_media_links_must_be_valid_json          # Custom validation for social media links
  validate :validate_tag_ids
  validate :policies_must_be_valid_json                    # Custom validation for policies

  before_validation :set_default_currency_code, on: :create


  scope :by_city, ->(city_id) {
    where(city_id: city_id) if city_id.present?
  }

  scope :by_min_date, ->(min_date) {
    range_start = Date.parse(min_date).beginning_of_day
    where("events.end_time >= ?", range_start)
  }

  scope :by_max_date, ->(max_date) {
    range_end = Date.parse(max_date).end_of_day
    where("events.start_time <= ?", range_end)
  }

  scope :by_min_price, ->(min_price) {
    joins(:ticket_types).where("ticket_types.price >= ?", min_price).distinct
  }

  scope :by_max_price, ->(max_price) {
    joins(:ticket_types).where("ticket_types.price <= ?", max_price).distinct
  }

  scope :by_min_discounted_price, ->(min_price) {
    joins(:ticket_types).where("ticket_types.current_effective_price >= ?", min_price).distinct
  }

  scope :by_max_discounted_price, ->(max_price) {
    joins(:ticket_types).where("ticket_types.current_effective_price <= ?", max_price).distinct
  }

  scope :by_tags, ->(tag_ids) {
    sanitized_tag_ids = Array(tag_ids).map(&:to_i).reject(&:zero?)

    if sanitized_tag_ids.present?
      joins(:tags).where(tags: { id: sanitized_tag_ids }).distinct
    else
      all
    end
}


  def end_time_after_start_time
    return if start_time.blank? || end_time.blank?

    if end_time <= start_time
      errors.add(:end_time, "must be after the start time")
    end
  end

  def validate_tag_ids
    return unless tag_ids.present?

    valid_tag_ids = Tag.where(id: tag_ids).pluck(:id)
    invalid_tag_ids = tag_ids - valid_tag_ids

    if invalid_tag_ids.any?
      errors.add(:tag_ids, "contains invalid tag IDs: #{invalid_tag_ids.join(', ')}")
    end
  end

  def social_media_links_must_be_valid_json
    return if social_media_links.blank?

    unless social_media_links.is_a?(Array) && social_media_links.all? { |link| link.is_a?(Hash) && link.key?("platform") && link.key?("link") }
      errors.add(:social_media_links, "must be an array of JSON objects with 'platform' and 'link'")
    end
  end

  def policies_must_be_valid_json
    return if policies.blank?

    unless policies.is_a?(Array) && policies.all? { |policy| policy.is_a?(Hash) && policy.key?("type") && policy.key?("details") }
      errors.add(:policies, "must be an array of JSON objects with 'type' and 'details'")
    end
  end

  # TODO: Can be done on the database level, if there are performace issues consider doing it
  def saved_by?(user)
    user&.is_customer? && saved_events.exists?(customer: user)
  end

  scope :search_with_relevance, ->(query, tag_weight = 3) {
    return none if query.blank?

    words = query.downcase.split(" ")

    # Build the SQL clauses for filtering
    sql_clauses = words.map do |word|
      <<~SQL.squish
        LOWER(events.name) LIKE ? OR
        LOWER(events.description) LIKE ? OR
        EXISTS (
          SELECT 1 FROM tags
          INNER JOIN events_tags ON tags.id = events_tags.tag_id
          WHERE events_tags.event_id = events.id AND LOWER(tags.name) LIKE ?
        )
      SQL
    end.join(" OR ")

    # Build the corresponding parameters
    sql_params = words.flat_map { |word| [ "%#{word}%", "%#{word}%", "%#{word}%" ] }
    # Construct the query
    select("events.*, (#{relevance_score_query(words, tag_weight)}) AS relevance")
      .where(sql_clauses, *sql_params)
      .group("events.id")
    .order("relevance DESC")
  }

  # Helper for relevance scoring
  def self.relevance_score_query(words, tag_weight = 3)
    words.map do |word|
      <<~SQL.squish
        (CASE WHEN LOWER(events.name) LIKE '%#{word}%' THEN 2 ELSE 0 END) +
        (CASE WHEN LOWER(events.description) LIKE '%#{word}%' THEN 1 ELSE 0 END) +
        (#{tag_weight} * (
          SELECT COUNT(*)
          FROM events_tags
          INNER JOIN tags ON tags.id = events_tags.tag_id
          WHERE events_tags.event_id = events.id AND LOWER(tags.name) LIKE '%#{word}%'
        ))
      SQL
    end.join(" + ")
  end

  scope :by_city, ->(city_id) {
    where(city_id: city_id) if city_id.present?
  }

  scope :by_date_range, ->(min_date, max_date) {
    where("end_time >= ?", min_date) if min_date.present?
    where("end_time <= ?", max_date) if max_date.present?
  }

  scope :by_price_range, ->(min_price, max_price) {
    joins(:ticket_types).where("ticket_types.price >= ?", min_price) if min_price.present?
    joins(:ticket_types).where("ticket_types.price <= ?", max_price) if max_price.present?
  }

  private

  def set_default_currency_code
    self.currency ||= city&.state&.currency_code
  end
end
