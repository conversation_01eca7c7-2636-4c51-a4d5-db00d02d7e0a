class MailSubscription < ApplicationRecord
  # Enums
  enum :subscription_type, { basic: 0 }, default: :basic, prefix: true

  # Validations
  validates :user_id, presence: true
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :subscription_type, presence: true
  validates :subscribed, inclusion: { in: [ true, false ] }

  # Associations
  belongs_to :user
end
