class Customer < User
  has_many :saved_events, foreign_key: :user_id
  has_many :events, through: :saved_events
  has_many :orders, foreign_key: :user_id
  has_one :basket, foreign_key: :user_id

  validates :first_name, presence: true, if: :not_visitor?
  validates :last_name, presence: true, if: :not_visitor?
  validates :visitor_token, presence: true, uniqueness: true, if: :visitor?

  def all_saved_events
    events
  end

  def visitor?
    email.blank? and visitor_token.present?
  end

  def not_visitor?
    not visitor?
  end

  def email_required?
    not_visitor?
  end

  def password_required?
    not_visitor?
  end
end
