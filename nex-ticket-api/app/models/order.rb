class Order < ApplicationRecord
  class PromoCodeError < StandardError; end

  belongs_to :customer, foreign_key: :user_id
  belongs_to :event
  has_many :order_items, dependent: :destroy
  belongs_to :promo_code, optional: true


  accepts_nested_attributes_for :order_items

  validates :order_id, presence: true, length: { minimum: 12 }, numericality: { only_integer: true }
  validates :user_id, presence: true
  validates :base_subtotal_amount, :promo_discount_amount, :subtotal_without_vat, :vat_value, :total,
  numericality: { greater_than_or_equal_to: 0 }, allow_nil: false

  before_validation :generate_order_id!, on: :create

  enum :status, { created: 0, processing: 1, success: 2, payment_failed: 3, cancelled: 4 }, default: :created, prefix: true
  enum :tickets_status, { order_not_paid: 0, pending: 1, generated: 1, generation_failed: 2 }, default: :order_not_paid, prefix: true

  def reserve_items!
    order_items.each(&:update_itemable_available_amount_on_save)
  end

  def release_items!
    order_items.each(&:update_itemable_available_amount_on_release)
  end

  def calculate_prices!
    ActiveRecord::Base.transaction do
      calculate_base_subtotal_amount!
      calculate_promo_discount_amount!
      calculate_subtotal_without_vat!
      calculate_vat!
      calculate_total!
    end
  end

  def apply_promo_code!(code_string)
    raise PromoCodeError, I18n.t("model.errors.promo_code_processing") unless self.status_processing?

    promo = PromoCode.available(Time.current).find_by(code: code_string)
    raise PromoCodeError, I18n.t("model.errors.promo_code_not_found") unless promo
    self.reload
    raise PromoCodeError, I18n.t("model.errors.promo_code_n_a") unless promo.applicable_to?(self)

    self.promo_code = promo
    calculate_prices!
    save!
    true
  rescue ActiveRecord::RecordInvalid => e
    self.promo_code = nil
    raise PromoCodeError, I18n.t("model.errors.promo_code_save_fail", message: e.errors.full_messages.join(", "))
  rescue => e
    self.promo_code = nil
    Rails.logger.error "Failed to apply promo code '#{code_string}': #{e.message}" if defined?(Rails)
    raise e if e.is_a?(PromoCodeError)
    raise PromoCodeError, I18n.t("model.errors.promo_code_apply_fail", message: e.message)
  end

  def remove_promo_code!
    raise PromoCodeError, I18n.t("model.errors.promo_code_remove_fail") unless self.status_processing?
    raise PromoCodeError, I18n.t("model.errors.no_promo_code_applied") unless self.promo_code_id?

    self.promo_code = nil
    calculate_prices!
    save!
    true
  rescue ActiveRecord::RecordInvalid => e
    raise PromoCodeError, I18n.t("model.errors.promo_code_order_save_fail", message: e.errors.full_messages.join(", "))
  rescue => e
     Rails.logger.error "Failed to remove promo code: #{e.message}" if defined?(Rails)
     raise PromoCodeError, I18n.t("model.errors.can_not_remove_promo_code", message: e.message)
  end

  def mark_as_success!(external_payment_gate_type, external_payment_gate_id)
    saved_successfully = false
    ActiveRecord::Base.transaction do
      self.external_payment_gate_type = external_payment_gate_type
      self.external_payment_gate_id = external_payment_gate_id
      self.status = :success
      self.tickets_status = :pending

      save!
      saved_successfully = true

      self.promo_code&.increment_usage!
      self.reserve_items!
      customer.basket&.destroy!
    end

    if saved_successfully
      begin
        TicketService.new.generate_and_send_tickets_from_order(self)
        self.tickets_status = :generated
        # Add job later to handle failed??
      rescue => e
        Rails.logger.error("Failed to generate/send tickets for Order #{self.id}: #{e.message}")
        self.tickets_status = :generation_failed
      end
    end

    true
  rescue ActiveRecord::RecordInvalid, ActiveRecord::RecordNotDestroyed => e
    Rails.logger.error("Failed to mark Order #{self.id} as success in DB: #{e.message}")
    raise e
  end

  def set_default_status!
    self.status ||= :created
  end

  def set_default_currency_code!
    self.currency_code ||= event.currency
  end

  private

  def calculate_base_subtotal_amount!
      self.base_subtotal_amount = order_items.to_a.sum(&:total_price)
  end

  def calculate_promo_discount_amount!
      current_promo = self.promo_code

      if current_promo&.present?
        self.promo_discount_amount = current_promo.calculate_discount(self)
      else
        self.promo_discount_amount = 0.0
      end
  end

  def calculate_subtotal_without_vat!
    calculated_subtotal = (self.base_subtotal_amount - self.promo_discount_amount).round(2)
    # Ensure non-negative
    self.subtotal_without_vat = [ calculated_subtotal, 0 ].max
  end

  def calculate_vat!
    state = event.city.state
    vat_percentage = state.vat_percentage
    if vat_percentage.nil?
      return
    end
    self.vat_percentage = vat_percentage
    self.vat_value = subtotal_without_vat * (vat_percentage / 100.0)
  end

  def calculate_total!
    self.total = subtotal_without_vat + vat_value
    if self.total <= 0
      raise ArgumentError, I18n.t("model.errors.total_cant_be_negative")
    end
  end

  def generate_order_id!
    self.order_id ||= ActiveRecord::Base.connection.select_value("SELECT nextval('order_id_seq')")
  end
end
