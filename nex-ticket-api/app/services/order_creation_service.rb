# app/services/order_creation_service.rb
class OrderCreationService
  attr_reader :customer, :order_params

  def initialize(customer, order_params)
    @customer = customer
    @order_params = order_params
  end

  def call
    ActiveRecord::Base.transaction do
      build_order
      build_order_items_from_basket
      @order.set_default_status!
      @order.set_default_currency_code!
      calculate_prices
      @order.save!
    end

    @order
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error("Order creation failed: #{e.message}")
    @order
  rescue ArgumentError => e
    Rails.logger.error("Order creation argument error: #{e.message}")
    @order ||= customer.orders.build(order_params)
    @order.errors.add(:base, e.message)
    @order
  end

  def success?
    @order&.persisted? && @order&.errors&.empty?
  end

  private

  def build_order
    event_id = order_params[:event_id]
    raise ArgumentError, "Event ID is missing" unless event_id

    @order = customer.orders.build(order_params)
  end

  def build_order_items_from_basket
    raise ArgumentError, "Customer basket is empty or missing" unless customer.basket&.basket_items&.present?

    event_id = @order.event_id

    customer.basket.basket_items.each do |basket_item|
      item_event_id = basket_item.itemable.try(:event_id)

      if item_event_id.nil? || item_event_id == event_id
        @order.order_items.build(
          order_itemable: basket_item.itemable,
          quantity: basket_item.quantity
        )
      end
    end

    raise ArgumentError, "No applicable items found in the basket for the selected event" if @order.order_items.empty?
  end

  def calculate_prices
    @order.order_items.each do |item|
      calculate_item_prices(item)
    end

    @order.calculate_prices!
  end

  def calculate_item_prices(item)
    price = 0.0
    platform_fee = 0.0
    organiser_price = 0.0

    if item.order_itemable.respond_to?(:discounted_price) && item.order_itemable_type == "TicketType"
      price = item.order_itemable.discounted_price
      organiser = get_organiser_from_order_item(item)

      if organiser&.profit_share.present?
          profit_share_percentage = organiser.profit_share / 100.0
          calculated_platform_fee = (price * profit_share_percentage).round(2)
          platform_fee = calculated_platform_fee
          organiser_price = price - platform_fee
      else
          Rails.logger.warn("Organiser or profit_share missing for OrderItemable ID: #{item.order_itemable.id}. Assigning full price to platform.")
          platform_fee = price
          organiser_price = 0.0
      end

    elsif item.order_itemable.respond_to?(:price_per_piece)
      price = item.order_itemable.price_per_piece
      organiser_price = 0.0
    else
       Rails.logger.warn("OrderItemable type #{item.order_itemable_type} with ID #{item.order_itemable.id} does not have a recognized price method.")
    end

    item.assign_attributes(
      organiser_price_per_piece: organiser_price,
      platform_fee_per_piece: platform_fee
    )

    item.calculate_price_per_piece!
    item.calculate_total_price!
  end

  def get_organiser_from_order_item(item)
    item.order_itemable&.event&.organiser
  end
end
