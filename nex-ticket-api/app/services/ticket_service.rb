class TicketService
  def generate_and_send_tickets_from_order(order)
    order.order_items.each do |order_item|
      next unless order_item.order_itemable_type == "TicketType"
      order_item.quantity.times do |index|
        Ticket.create(
          ticket_id: "#{order.order_id}-#{order_item.id}-#{index + 1}",
          first_name: order.first_name,
          last_name: order.last_name,
          order_item: order_item
        )
      end
    end

    UserMailer.with(order: order).send_ticket_mail.deliver_now
  end
end
