class TicketsPdfService
  require "prawn"
  require "prawn-svg"
  require "rqrcode"

  def create_pdf(order)
    event = order.event
    ticket_type = order.order_items.first.order_itemable

    pdf = Prawn::Document.new(page_size: "A6", margin: 20)

    # Background color
    pdf.canvas do
      pdf.fill_color "F0F0F0"
      pdf.fill_rectangle [ 0, pdf.bounds.height ], pdf.bounds.width, pdf.bounds.height
    end
    pdf.fill_color "000000"

    # Header
    pdf.bounding_box([ 0, pdf.bounds.height - 20 ], width: pdf.bounds.width, height: 50) do
      pdf.text "YOUR TICKET", size: 18, style: :bold, align: :center, color: "333333"
      pdf.move_down 5
      pdf.text "Please show it on your phone when you arrive at the venue", size: 10, align: :center, color: "666666"
      pdf.stroke_horizontal_rule
    end
    pdf.move_down 20

    # Event details
    pdf.text "Event:", size: 14, style: :bold, color: "333333"
    pdf.text event.name, size: 12, color: "000000"
    pdf.move_down 10

    pdf.text "Date:", size: 14, style: :bold, color: "333333"
    pdf.text event.start_time.strftime("%A, %d %B %Y"), size: 12, color: "000000"
    pdf.move_down 10

    pdf.text "Time:", size: 14, style: :bold, color: "333333"
    pdf.text event.start_time.strftime("%I:%M %p"), size: 12, color: "000000"
    pdf.move_down 10

    pdf.text "Venue:", size: 14, style: :bold, color: "333333"
    pdf.text event.venue_name, size: 12, color: "000000"
    pdf.move_down 20

    # Attendee details
    pdf.text "Attendee:", size: 14, style: :bold, color: "333333"
    pdf.text "#{order.first_name} #{order.last_name}", size: 12, color: "000000"
    pdf.move_down 10

    # Ticket details
    pdf.text "Ticket Type:", size: 14, style: :bold, color: "333333"
    pdf.text ticket_type.name, size: 12, color: "000000"
    pdf.move_down 10

    # QR Code
    if defined?(RQRCode::QRCode)
      qr_content = <<~QR_CONTENT
        First Name: #{order.first_name}
        Last Name: #{order.last_name}
        Event: #{event.name}
        Ticket Type: #{ticket_type.name}
        Venue: #{event.venue_name}
        Date: #{event.start_time.strftime('%d-%m-%Y')}
        Time: #{event.start_time.strftime('%I:%M %p')}
      QR_CONTENT

      qrcode = RQRCode::QRCode.new(qr_content, level: :h)
      svg = qrcode.as_svg(offset: 0, color: "000", shape_rendering: "crispEdges", module_size: 6)

      temp_svg_file = Tempfile.new([ "qrcode", ".svg" ])
      temp_svg_file.write(svg)
      temp_svg_file.close

      pdf.text "QR Code:", size: 14, style: :bold, color: "333333", align: :center
      pdf.move_down 10

      qr_width = 100
      qr_x = (pdf.bounds.width - qr_width) / 2
      qr_y = pdf.cursor - 20

      pdf.svg IO.read(temp_svg_file.path), at: [ qr_x, qr_y ], width: qr_width

      temp_svg_file.unlink
    end

    # Footer
    pdf.move_down 30
    pdf.stroke_horizontal_rule
    pdf.move_down 10
    pdf.text "Thank you for using TicketPie!", size: 12, align: :center, color: "333333"
    pdf.text "Contact <NAME_EMAIL>", size: 10, align: :center, color: "666666"

    pdf.render
  end
end
