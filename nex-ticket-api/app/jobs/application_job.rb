require "sidekiq"
require "activerecord-import"

class ApplicationJob
  include Sidekiq::Worker

  sidekiq_options retry: 3, queue: :default

  def perform(*args)
    start_time = Time.current
    job_name = self.class.name
    logger.info "Starting #{job_name} with args: #{args.inspect}"

    begin
      execute(*args)

      duration = Time.current - start_time
      logger.info "Finished #{job_name} successfully. Duration: #{duration.round(2)}s"

    rescue StandardError => e
      duration = Time.current - start_time
      logger.error "Error in #{job_name} after #{duration.round(2)}s: #{e.message}"
      logger.error "Args: #{args.inspect}"
      logger.error e.backtrace.join("\n")

      raise e
      # Ensure block could be used for cleanup if needed,
      # but logging completion here covers success/failure paths.
    end
  end

  def execute(*args)
    raise NotImplementedError, "#{self.class.name} must implement the 'execute' method."
  end


  private

  def logger
    Sidekiq.logger rescue Rails.logger
  end
end
